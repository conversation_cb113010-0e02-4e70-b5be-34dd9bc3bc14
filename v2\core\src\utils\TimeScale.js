/**
 * TimeScale - 时间轴计算引擎
 * 
 * 核心功能：
 * - 高精度的时间坐标系统
 * - 支持动态缩放的时间轴
 * - 多种时间视图模式 (日/周/月/季度)
 * - 日期与像素坐标双向转换
 * - 可视区域和缓冲区计算
 * - 时间轴网格计算逻辑
 */

import DateUtils from './DateUtils.js';

export default class TimeScale {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   * @param {Date} options.startDate - 开始日期
   * @param {Date} options.endDate - 结束日期
   * @param {string} options.viewMode - 视图模式 (day/week/month/quarter)
   * @param {number} options.pixelsPerDay - 每天的像素数
   * @param {number} options.zoomLevel - 缩放级别
   * @param {Object} options.viewport - 视口信息
   */
  constructor(options = {}) {
    // 基础配置
    this.startDate = options.startDate ? new Date(options.startDate) : new Date();
    this.endDate = options.endDate ? new Date(options.endDate) : new Date();
    this.viewMode = options.viewMode || DateUtils.VIEW_MODES.DAY;
    this.pixelsPerDay = options.pixelsPerDay || 30;
    this.zoomLevel = options.zoomLevel || 1.0;
    
    // 视口配置
    this.viewport = {
      width: options.viewport?.width || 1000,
      height: options.viewport?.height || 600,
      scrollX: options.viewport?.scrollX || 0,
      scrollY: options.viewport?.scrollY || 0,
      ...options.viewport
    };

    // 缓冲区配置
    this.bufferConfig = {
      horizontal: options.bufferConfig?.horizontal || 200, // 水平缓冲区像素
      vertical: options.bufferConfig?.vertical || 100,     // 垂直缓冲区像素
      ...options.bufferConfig
    };

    // 网格配置
    this.gridConfig = {
      showMajorGrid: true,
      showMinorGrid: true,
      majorGridInterval: 1,
      minorGridInterval: 1,
      adaptiveDensity: true,
      ...options.gridConfig
    };

    // 时间轴显示配置
    this.timelineConfig = {
      showWeekday: options.timelineConfig?.showWeekday ?? true,
      adaptiveWidth: options.timelineConfig?.adaptiveWidth ?? true,
      minScaleWidth: options.timelineConfig?.minScaleWidth ?? 30,
      maxScaleWidth: options.timelineConfig?.maxScaleWidth ?? 80,
      weekdayFormat: options.timelineConfig?.weekdayFormat ?? 'short',
      ...options.timelineConfig
    };

    // 内部状态
    this._scales = [];
    this._totalWidth = 0;
    this._visibleRange = { start: 0, end: 0 };
    this._isDirty = true;

    // 初始化
    this._initialize();
  }

  /**
   * 初始化时间轴
   * @private
   */
  _initialize() {
    this._validateDates();
    this._calculateScales();
    this._calculateDimensions();
    this._isDirty = false;
  }

  /**
   * 验证日期范围
   * @private
   */
  _validateDates() {
    if (this.startDate >= this.endDate) {
      throw new Error('开始日期必须小于结束日期');
    }

    // 确保日期对象有效
    if (isNaN(this.startDate.getTime()) || isNaN(this.endDate.getTime())) {
      throw new Error('无效的日期对象');
    }
  }

  /**
   * 计算时间刻度
   * @private
   */
  _calculateScales() {
    const unit = DateUtils.getTimeUnit(this.viewMode);
    this._scales = [];
    
    let current = new Date(this.startDate);
    
    // 根据视图模式调整起始位置到合适的边界
    switch (this.viewMode) {
      case DateUtils.VIEW_MODES.WEEK:
        current = DateUtils.getWeekStart(current);
        break;
      case DateUtils.VIEW_MODES.MONTH:
        current = DateUtils.getMonthStart(current);
        break;
      case DateUtils.VIEW_MODES.QUARTER:
        current = DateUtils.getQuarterStart(current);
        break;
      default:
        // 日视图保持原始日期
        current.setHours(0, 0, 0, 0);
        break;
    }

    let index = 0;
    while (current <= this.endDate) {
      const scale = {
        date: new Date(current),
        label: DateUtils.formatTimeLabel(current, this.viewMode),
        x: this._calculateScaleX(index),
        index: index,
        isMajor: this._isMajorScale(current, index),
        isVisible: false // 将在可视区域计算中更新
      };

      this._scales.push(scale);
      current = unit.increment(current);
      index++;
    }
  }

  /**
   * 计算刻度的X坐标
   * @private
   * @param {number} index - 刻度索引
   * @returns {number} X坐标
   */
  _calculateScaleX(index) {
    const unit = DateUtils.getTimeUnit(this.viewMode);
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;
    return index * scaledPixelsPerDay * unit.duration;
  }

  /**
   * 判断是否为主要刻度（智能密度控制）
   * @private
   * @param {Date} date - 日期
   * @param {number} index - 索引
   * @returns {boolean} 是否为主要刻度
   */
  _isMajorScale(date, index) {
    if (!this.gridConfig.adaptiveDensity) {
      return index % this.gridConfig.majorGridInterval === 0;
    }

    // 根据缩放级别和视图模式智能调整密度
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;
    const density = this._calculateOptimalDensity(scaledPixelsPerDay);

    switch (this.viewMode) {
      case DateUtils.VIEW_MODES.DAY:
        if (scaledPixelsPerDay < 15) {
          // 密度很低时，只显示月初
          return date.getDate() === 1;
        } else if (scaledPixelsPerDay < 30) {
          // 中等密度，显示周一
          return date.getDay() === 1;
        } else {
          // 高密度，显示每天但突出周一
          return date.getDay() === 1 || index % density.major === 0;
        }

      case DateUtils.VIEW_MODES.WEEK:
        if (scaledPixelsPerDay < 50) {
          return date.getMonth() % 3 === 0 && date.getDate() <= 7; // 季度开始
        } else {
          return date.getDate() === 1; // 月初
        }

      case DateUtils.VIEW_MODES.MONTH:
        if (scaledPixelsPerDay < 80) {
          return date.getMonth() === 0; // 年初
        } else {
          return date.getMonth() % 3 === 0; // 季度开始
        }

      case DateUtils.VIEW_MODES.QUARTER:
        return date.getMonth() === 0; // 年初

      default:
        return index % density.major === 0;
    }
  }

  /**
   * 计算最优刻度密度
   * @private
   * @param {number} pixelsPerDay - 每天像素数
   * @returns {Object} 密度配置
   */
  _calculateOptimalDensity(pixelsPerDay) {
    // 基于像素密度计算最优的主次刻度间隔
    if (pixelsPerDay < 10) {
      return { major: 7, minor: 0 }; // 只显示主要刻度
    } else if (pixelsPerDay < 20) {
      return { major: 5, minor: 1 };
    } else if (pixelsPerDay < 40) {
      return { major: 3, minor: 1 };
    } else if (pixelsPerDay < 80) {
      return { major: 2, minor: 1 };
    } else {
      return { major: 1, minor: 1 }; // 显示所有刻度
    }
  }

  /**
   * 获取智能标签配置
   * @param {Object} scale - 时间刻度
   * @param {number} availableWidth - 可用宽度
   * @returns {Object} 标签配置
   */
  getSmartLabelConfig(scale, availableWidth = 100) {
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;

    // 根据可用空间和缩放级别决定标签格式
    const labelConfig = {
      show: true,
      format: 'auto',
      rotation: 0,
      fontSize: 12,
      color: '#666',
      weight: 'normal'
    };

    // 空间不足时隐藏标签
    if (availableWidth < 30) {
      labelConfig.show = false;
      return labelConfig;
    }

    // 根据视图模式和密度调整标签
    switch (this.viewMode) {
      case DateUtils.VIEW_MODES.DAY:
        if (scaledPixelsPerDay < 20) {
          labelConfig.format = 'MM/DD';
          labelConfig.rotation = availableWidth < 50 ? 45 : 0;
        } else if (scaledPixelsPerDay < 60) {
          labelConfig.format = 'MM/DD';
        } else {
          labelConfig.format = 'MM/DD ddd';
        }
        break;

      case DateUtils.VIEW_MODES.WEEK:
        if (scaledPixelsPerDay < 40) {
          labelConfig.format = 'MM/DD';
          labelConfig.rotation = 45;
        } else {
          labelConfig.format = 'MM/DD (周W)';
        }
        break;

      case DateUtils.VIEW_MODES.MONTH:
        if (scaledPixelsPerDay < 60) {
          labelConfig.format = 'YYYY/MM';
        } else {
          labelConfig.format = 'YYYY年MM月';
        }
        break;

      case DateUtils.VIEW_MODES.QUARTER:
        labelConfig.format = 'YYYY年QQ季度';
        break;
    }

    // 主要刻度使用粗体
    if (scale.isMajor) {
      labelConfig.weight = 'bold';
      labelConfig.color = '#333';
      labelConfig.fontSize = 13;
    }

    return labelConfig;
  }

  /**
   * 获取视图模式的详细信息
   * @param {string} viewMode - 视图模式
   * @returns {Object} 视图模式信息
   */
  getViewModeInfo(viewMode = this.viewMode) {
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;

    const viewModeInfos = {
      [DateUtils.VIEW_MODES.DAY]: {
        name: '日视图',
        unit: '天',
        minPixelsPerDay: 10,
        maxPixelsPerDay: 200,
        optimalPixelsPerDay: 30,
        scaleInterval: 1,
        labelFormat: 'MM/DD',
        description: '按天显示，适合查看详细的日程安排'
      },
      [DateUtils.VIEW_MODES.WEEK]: {
        name: '周视图',
        unit: '周',
        minPixelsPerDay: 15,
        maxPixelsPerDay: 150,
        optimalPixelsPerDay: 100,
        scaleInterval: 7,
        labelFormat: 'MM/DD',
        description: '按周显示，适合查看周期性任务'
      },
      [DateUtils.VIEW_MODES.MONTH]: {
        name: '月视图',
        unit: '月',
        minPixelsPerDay: 20,
        maxPixelsPerDay: 120,
        optimalPixelsPerDay: 120,
        scaleInterval: 30,
        labelFormat: 'YYYY/MM',
        description: '按月显示，适合查看长期规划'
      },
      [DateUtils.VIEW_MODES.QUARTER]: {
        name: '季度视图',
        unit: '季度',
        minPixelsPerDay: 30,
        maxPixelsPerDay: 100,
        optimalPixelsPerDay: 300,
        scaleInterval: 90,
        labelFormat: 'YYYY-Q',
        description: '按季度显示，适合查看年度规划'
      }
    };

    const info = viewModeInfos[viewMode] || viewModeInfos[DateUtils.VIEW_MODES.DAY];

    return {
      ...info,
      currentPixelsPerDay: scaledPixelsPerDay,
      isOptimal: scaledPixelsPerDay >= info.minPixelsPerDay && scaledPixelsPerDay <= info.maxPixelsPerDay,
      densityLevel: this._getDensityLevel(scaledPixelsPerDay, info)
    };
  }

  /**
   * 获取密度级别
   * @private
   * @param {number} pixelsPerDay - 每天像素数
   * @param {Object} viewInfo - 视图信息
   * @returns {string} 密度级别
   */
  _getDensityLevel(pixelsPerDay, viewInfo) {
    if (pixelsPerDay < viewInfo.minPixelsPerDay) return 'too-low';
    if (pixelsPerDay > viewInfo.maxPixelsPerDay) return 'too-high';
    if (pixelsPerDay < viewInfo.optimalPixelsPerDay * 0.7) return 'low';
    if (pixelsPerDay > viewInfo.optimalPixelsPerDay * 1.3) return 'high';
    return 'optimal';
  }

  /**
   * 计算总体尺寸
   * @private
   */
  _calculateDimensions() {
    if (this._scales.length === 0) {
      this._totalWidth = 0;
      return;
    }

    const lastScale = this._scales[this._scales.length - 1];
    const unit = DateUtils.getTimeUnit(this.viewMode);
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;

    // 总宽度 = 最后一个刻度的X坐标 + 一个单位的宽度
    this._totalWidth = lastScale.x + scaledPixelsPerDay * unit.duration;
  }

  // ==================== 公共API方法 ====================

  /**
   * 日期转换为X坐标（高精度）
   * @param {Date} date - 目标日期
   * @param {Object} options - 转换选项
   * @param {boolean} options.subPixel - 是否启用亚像素精度
   * @param {boolean} options.clamp - 是否限制在有效范围内
   * @returns {number} X坐标（像素）
   */
  dateToX(date, options = {}) {
    if (!date || isNaN(date.getTime())) {
      return 0;
    }

    const { subPixel = true, clamp = false } = options;

    // 高精度时间差计算（毫秒级）
    const timeDiff = date.getTime() - this.startDate.getTime();
    const daysDiff = timeDiff / (24 * 60 * 60 * 1000);

    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;
    let x = daysDiff * scaledPixelsPerDay;

    // 亚像素精度处理
    if (!subPixel) {
      x = Math.round(x);
    }

    // 边界限制
    if (clamp) {
      x = Math.max(0, Math.min(this.getTotalWidth(), x));
    }

    return x;
  }

  /**
   * X坐标转换为日期（高精度）
   * @param {number} x - X坐标（像素）
   * @param {Object} options - 转换选项
   * @param {boolean} options.precise - 是否使用精确计算
   * @param {boolean} options.clamp - 是否限制在有效范围内
   * @returns {Date} 对应的日期
   */
  xToDate(x, options = {}) {
    const { precise = true, clamp = false } = options;

    // 边界限制
    if (clamp) {
      x = Math.max(0, Math.min(this.getTotalWidth(), x));
    }

    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;
    const days = x / scaledPixelsPerDay;

    if (precise) {
      // 高精度计算：直接使用毫秒
      const milliseconds = days * 24 * 60 * 60 * 1000;
      return new Date(this.startDate.getTime() + milliseconds);
    } else {
      // 标准计算：使用天数
      return DateUtils.addDays(this.startDate, Math.round(days));
    }
  }

  /**
   * 批量日期转换为X坐标（性能优化）
   * @param {Date[]} dates - 日期数组
   * @param {Object} options - 转换选项
   * @returns {number[]} X坐标数组
   */
  datesToX(dates, options = {}) {
    if (!Array.isArray(dates)) return [];

    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;
    const startTime = this.startDate.getTime();
    const { subPixel = true, clamp = false } = options;
    const totalWidth = this.getTotalWidth();

    return dates.map(date => {
      if (!date || isNaN(date.getTime())) return 0;

      const timeDiff = date.getTime() - startTime;
      const daysDiff = timeDiff / (24 * 60 * 60 * 1000);
      let x = daysDiff * scaledPixelsPerDay;

      if (!subPixel) x = Math.round(x);
      if (clamp) x = Math.max(0, Math.min(totalWidth, x));

      return x;
    });
  }

  /**
   * 批量X坐标转换为日期（性能优化）
   * @param {number[]} xCoords - X坐标数组
   * @param {Object} options - 转换选项
   * @returns {Date[]} 日期数组
   */
  xToDates(xCoords, options = {}) {
    if (!Array.isArray(xCoords)) return [];

    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;
    const startTime = this.startDate.getTime();
    const { precise = true, clamp = false } = options;
    const totalWidth = this.getTotalWidth();

    return xCoords.map(x => {
      if (clamp) x = Math.max(0, Math.min(totalWidth, x));

      const days = x / scaledPixelsPerDay;

      if (precise) {
        const milliseconds = days * 24 * 60 * 60 * 1000;
        return new Date(startTime + milliseconds);
      } else {
        return DateUtils.addDays(this.startDate, Math.round(days));
      }
    });
  }

  /**
   * 获取指定日期范围的X坐标范围（高精度）
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @param {Object} options - 转换选项
   * @returns {Object} {start: number, end: number, width: number, center: number}
   */
  getDateRangeX(startDate, endDate, options = {}) {
    const startX = this.dateToX(startDate, options);
    const endX = this.dateToX(endDate, options);

    const start = Math.min(startX, endX);
    const end = Math.max(startX, endX);
    const width = end - start;
    const center = start + width / 2;

    return { start, end, width, center };
  }

  /**
   * 获取时间单位的像素宽度
   * @param {string} unit - 时间单位 (hour, day, week, month, quarter, year)
   * @returns {number} 像素宽度
   */
  getUnitWidth(unit = 'day') {
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;

    const unitWidths = {
      hour: scaledPixelsPerDay / 24,
      day: scaledPixelsPerDay,
      week: scaledPixelsPerDay * 7,
      month: scaledPixelsPerDay * 30, // 平均值
      quarter: scaledPixelsPerDay * 90, // 平均值
      year: scaledPixelsPerDay * 365 // 平均值
    };

    return unitWidths[unit] || scaledPixelsPerDay;
  }

  /**
   * 对齐到最近的时间单位
   * @param {Date} date - 目标日期
   * @param {string} unit - 对齐单位
   * @param {string} direction - 对齐方向 ('floor', 'ceil', 'round')
   * @returns {Date} 对齐后的日期
   */
  alignToUnit(date, unit = 'day', direction = 'round') {
    if (!date || isNaN(date.getTime())) return new Date();

    const aligned = new Date(date);

    switch (unit) {
      case 'hour':
        aligned.setMinutes(0, 0, 0);
        if (direction === 'ceil' && aligned.getTime() !== date.getTime()) {
          aligned.setHours(aligned.getHours() + 1);
        }
        break;

      case 'day':
        aligned.setHours(0, 0, 0, 0);
        if (direction === 'ceil' && aligned.getTime() !== date.getTime()) {
          aligned.setDate(aligned.getDate() + 1);
        }
        break;

      case 'week':
        const weekStart = DateUtils.getWeekStart(aligned);
        if (direction === 'ceil' && weekStart.getTime() !== date.getTime()) {
          weekStart.setDate(weekStart.getDate() + 7);
        }
        return weekStart;

      case 'month':
        aligned.setDate(1);
        aligned.setHours(0, 0, 0, 0);
        if (direction === 'ceil' && aligned.getTime() !== date.getTime()) {
          aligned.setMonth(aligned.getMonth() + 1);
        }
        break;

      case 'quarter':
        const quarterStart = DateUtils.getQuarterStart(aligned);
        if (direction === 'ceil' && quarterStart.getTime() !== date.getTime()) {
          quarterStart.setMonth(quarterStart.getMonth() + 3);
        }
        return quarterStart;

      case 'year':
        aligned.setMonth(0, 1);
        aligned.setHours(0, 0, 0, 0);
        if (direction === 'ceil' && aligned.getTime() !== date.getTime()) {
          aligned.setFullYear(aligned.getFullYear() + 1);
        }
        break;
    }

    return aligned;
  }

  /**
   * 计算两个X坐标之间的时间差
   * @param {number} x1 - 起始X坐标
   * @param {number} x2 - 结束X坐标
   * @param {string} unit - 返回单位 (milliseconds, seconds, minutes, hours, days)
   * @returns {number} 时间差
   */
  getTimeDiffFromX(x1, x2, unit = 'days') {
    const date1 = this.xToDate(x1, { precise: true });
    const date2 = this.xToDate(x2, { precise: true });

    const diffMs = Math.abs(date2.getTime() - date1.getTime());

    switch (unit) {
      case 'milliseconds': return diffMs;
      case 'seconds': return diffMs / 1000;
      case 'minutes': return diffMs / (1000 * 60);
      case 'hours': return diffMs / (1000 * 60 * 60);
      case 'days': return diffMs / (1000 * 60 * 60 * 24);
      default: return diffMs / (1000 * 60 * 60 * 24);
    }
  }

  /**
   * 获取可视区域内的时间刻度（优化版）
   * @param {number} viewportX - 视口X偏移
   * @param {number} viewportWidth - 视口宽度
   * @param {Object} options - 选项
   * @returns {Array} 可视区域内的刻度数组
   */
  getVisibleScales(viewportX = this.viewport.scrollX, viewportWidth = this.viewport.width, options = {}) {
    const { includeBuffer = true, maxResults = 1000 } = options;

    const bufferX = includeBuffer ? this.bufferConfig.horizontal : 0;
    const startX = Math.max(0, viewportX - bufferX);
    const endX = Math.min(this.getTotalWidth(), viewportX + viewportWidth + bufferX);

    // 使用二分查找优化性能
    const startIndex = this._findScaleIndex(startX);
    const endIndex = this._findScaleIndex(endX);

    const visibleScales = [];
    const actualEndIndex = Math.min(endIndex + 1, this._scales.length, startIndex + maxResults);

    for (let i = startIndex; i < actualEndIndex; i++) {
      const scale = this._scales[i];
      if (scale.x >= startX && scale.x <= endX) {
        visibleScales.push({
          ...scale,
          isVisible: scale.x >= viewportX && scale.x <= viewportX + viewportWidth,
          distanceFromCenter: Math.abs(scale.x - (viewportX + viewportWidth / 2))
        });
      }
    }

    return visibleScales;
  }

  /**
   * 使用二分查找找到最接近X坐标的刻度索引
   * @private
   * @param {number} x - X坐标
   * @returns {number} 刻度索引
   */
  _findScaleIndex(x) {
    if (this._scales.length === 0) return 0;

    let left = 0;
    let right = this._scales.length - 1;

    while (left < right) {
      const mid = Math.floor((left + right) / 2);
      if (this._scales[mid].x < x) {
        left = mid + 1;
      } else {
        right = mid;
      }
    }

    return Math.max(0, left - 1);
  }

  /**
   * 获取详细的可视区域信息
   * @param {number} viewportX - 视口X偏移
   * @param {number} viewportWidth - 视口宽度
   * @param {number} viewportY - 视口Y偏移
   * @param {number} viewportHeight - 视口高度
   * @returns {Object} 详细的可视区域信息
   */
  getDetailedVisibleRange(viewportX = this.viewport.scrollX, viewportWidth = this.viewport.width,
                         viewportY = this.viewport.scrollY, viewportHeight = this.viewport.height) {
    const bufferX = this.bufferConfig.horizontal;
    const bufferY = this.bufferConfig.vertical;
    const totalWidth = this.getTotalWidth();

    // 计算水平范围
    const horizontalRange = {
      viewport: {
        start: viewportX,
        end: viewportX + viewportWidth,
        width: viewportWidth,
        center: viewportX + viewportWidth / 2
      },
      buffer: {
        start: Math.max(0, viewportX - bufferX),
        end: Math.min(totalWidth, viewportX + viewportWidth + bufferX),
        width: Math.min(totalWidth, viewportX + viewportWidth + bufferX) - Math.max(0, viewportX - bufferX)
      },
      total: {
        start: 0,
        end: totalWidth,
        width: totalWidth
      }
    };

    // 计算垂直范围
    const verticalRange = {
      viewport: {
        start: viewportY,
        end: viewportY + viewportHeight,
        height: viewportHeight,
        center: viewportY + viewportHeight / 2
      },
      buffer: {
        start: Math.max(0, viewportY - bufferY),
        end: viewportY + viewportHeight + bufferY,
        height: viewportHeight + 2 * bufferY
      }
    };

    // 计算日期范围
    const dateRange = {
      viewport: {
        start: this.xToDate(horizontalRange.viewport.start, { precise: true }),
        end: this.xToDate(horizontalRange.viewport.end, { precise: true })
      },
      buffer: {
        start: this.xToDate(horizontalRange.buffer.start, { precise: true }),
        end: this.xToDate(horizontalRange.buffer.end, { precise: true })
      },
      total: {
        start: this.startDate,
        end: this.endDate
      }
    };

    // 计算滚动信息
    const scrollInfo = {
      horizontal: {
        position: viewportX,
        percentage: totalWidth > 0 ? (viewportX / (totalWidth - viewportWidth)) * 100 : 0,
        canScrollLeft: viewportX > 0,
        canScrollRight: viewportX < totalWidth - viewportWidth,
        maxScroll: Math.max(0, totalWidth - viewportWidth)
      },
      vertical: {
        position: viewportY,
        percentage: 0, // 将在集成时计算
        canScrollUp: viewportY > 0,
        canScrollDown: true // 将在集成时计算
      }
    };

    // 性能统计
    const performance = {
      visibleScales: this.getVisibleScales(viewportX, viewportWidth, { includeBuffer: false }).length,
      bufferedScales: this.getVisibleScales(viewportX, viewportWidth, { includeBuffer: true }).length,
      totalScales: this._scales.length,
      renderRatio: this._scales.length > 0 ?
        (this.getVisibleScales(viewportX, viewportWidth, { includeBuffer: true }).length / this._scales.length) : 0
    };

    return {
      horizontal: horizontalRange,
      vertical: verticalRange,
      dates: dateRange,
      scroll: scrollInfo,
      performance
    };
  }

  /**
   * 获取智能网格线配置
   * @param {number} viewportX - 视口X偏移
   * @param {number} viewportWidth - 视口宽度
   * @param {Object} options - 网格选项
   * @returns {Object} 网格线配置
   */
  getGridLines(viewportX = this.viewport.scrollX, viewportWidth = this.viewport.width, options = {}) {
    const {
      includeSubGrid = false,
      alignToPixel = true,
      optimizePerformance = true
    } = options;

    const visibleScales = this.getVisibleScales(viewportX, viewportWidth);
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;

    const gridLines = {
      major: [],
      minor: [],
      sub: [],
      background: this._getBackgroundGrid(viewportX, viewportWidth),
      performance: {
        totalLines: 0,
        visibleLines: 0,
        culledLines: 0
      }
    };

    // 性能优化：当网格线过密时进行抽样
    const maxLines = optimizePerformance ? 200 : Infinity;
    const step = Math.max(1, Math.ceil(visibleScales.length / maxLines));

    visibleScales.forEach((scale, index) => {
      // 性能优化：跳过部分网格线
      if (optimizePerformance && index % step !== 0 && !scale.isMajor) {
        gridLines.performance.culledLines++;
        return;
      }

      const lineConfig = this._createGridLineConfig(scale, alignToPixel);
      gridLines.performance.totalLines++;

      if (scale.isMajor && this.gridConfig.showMajorGrid) {
        gridLines.major.push({
          ...lineConfig,
          type: 'major',
          weight: this._getMajorLineWeight(scale),
          opacity: this._getMajorLineOpacity(scale, scaledPixelsPerDay)
        });
        gridLines.performance.visibleLines++;
      } else if (this.gridConfig.showMinorGrid) {
        gridLines.minor.push({
          ...lineConfig,
          type: 'minor',
          weight: 1,
          opacity: this._getMinorLineOpacity(scaledPixelsPerDay)
        });
        gridLines.performance.visibleLines++;
      }
    });

    // 添加子网格（小时级别）
    if (includeSubGrid && this.viewMode === DateUtils.VIEW_MODES.DAY && scaledPixelsPerDay > 60) {
      gridLines.sub = this._generateSubGrid(viewportX, viewportWidth, alignToPixel);
    }

    return gridLines;
  }

  /**
   * 创建网格线配置
   * @private
   * @param {Object} scale - 时间刻度
   * @param {boolean} alignToPixel - 是否对齐到像素
   * @returns {Object} 网格线配置
   */
  _createGridLineConfig(scale, alignToPixel) {
    let x = scale.x;

    if (alignToPixel) {
      x = Math.round(x) + 0.5; // 对齐到像素边界，避免模糊
    }

    return {
      x,
      date: scale.date,
      label: scale.label,
      index: scale.index,
      labelConfig: this.getSmartLabelConfig(scale, this._getAvailableLabelWidth(scale))
    };
  }

  /**
   * 获取主要网格线权重
   * @private
   * @param {Object} scale - 时间刻度
   * @returns {number} 线条权重
   */
  _getMajorLineWeight(scale) {
    switch (this.viewMode) {
      case DateUtils.VIEW_MODES.DAY:
        return scale.date.getDay() === 1 ? 2 : 1.5; // 周一更粗
      case DateUtils.VIEW_MODES.WEEK:
        return scale.date.getDate() === 1 ? 2 : 1.5; // 月初更粗
      case DateUtils.VIEW_MODES.MONTH:
        return scale.date.getMonth() === 0 ? 2 : 1.5; // 年初更粗
      case DateUtils.VIEW_MODES.QUARTER:
        return 2;
      default:
        return 1.5;
    }
  }

  /**
   * 获取主要网格线透明度
   * @private
   * @param {Object} scale - 时间刻度
   * @param {number} pixelsPerDay - 每天像素数
   * @returns {number} 透明度
   */
  _getMajorLineOpacity(scale, pixelsPerDay) {
    // 根据密度和重要性调整透明度
    let baseOpacity;
    if (pixelsPerDay < 20) baseOpacity = 0.8;
    else if (pixelsPerDay < 40) baseOpacity = 0.6;
    else baseOpacity = 0.4;

    // 重要日期（如月初、年初）增加透明度
    if (this.viewMode === DateUtils.VIEW_MODES.DAY && scale.date.getDate() === 1) {
      baseOpacity += 0.2;
    } else if (this.viewMode === DateUtils.VIEW_MODES.MONTH && scale.date.getMonth() === 0) {
      baseOpacity += 0.2;
    }

    return Math.min(1.0, baseOpacity);
  }

  /**
   * 获取次要网格线透明度
   * @private
   * @param {number} pixelsPerDay - 每天像素数
   * @returns {number} 透明度
   */
  _getMinorLineOpacity(pixelsPerDay) {
    if (pixelsPerDay < 15) return 0; // 太密时隐藏
    if (pixelsPerDay < 30) return 0.1;
    if (pixelsPerDay < 60) return 0.2;
    return 0.3;
  }

  /**
   * 生成子网格（小时级别）
   * @private
   * @param {number} viewportX - 视口X偏移
   * @param {number} viewportWidth - 视口宽度
   * @param {boolean} alignToPixel - 是否对齐到像素
   * @returns {Array} 子网格线数组
   */
  _generateSubGrid(viewportX, viewportWidth, alignToPixel) {
    const subGrid = [];
    const hourWidth = this.getUnitWidth('hour');

    if (hourWidth < 5) return subGrid; // 太小时不显示

    const startDate = this.xToDate(viewportX);
    const endDate = this.xToDate(viewportX + viewportWidth);

    let current = new Date(startDate);
    current.setMinutes(0, 0, 0);

    while (current <= endDate) {
      const x = this.dateToX(current);

      if (x >= viewportX && x <= viewportX + viewportWidth) {
        subGrid.push({
          x: alignToPixel ? Math.round(x) + 0.5 : x,
          date: new Date(current),
          label: current.getHours() + ':00',
          type: 'sub',
          weight: 0.5,
          opacity: 0.15
        });
      }

      current.setHours(current.getHours() + 1);
    }

    return subGrid;
  }

  /**
   * 获取背景网格配置
   * @private
   * @param {number} viewportX - 视口X偏移
   * @param {number} viewportWidth - 视口宽度
   * @returns {Object} 背景网格配置
   */
  _getBackgroundGrid(viewportX, viewportWidth) {
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;

    // 根据视口大小调整网格密度
    const densityFactor = Math.min(1.0, viewportWidth / 1000);
    const adjustedSize = Math.max(10, scaledPixelsPerDay / 3) * densityFactor;

    return {
      show: scaledPixelsPerDay > 20,
      pattern: this._getGridPattern(),
      opacity: Math.min(0.1, scaledPixelsPerDay / 200) * densityFactor,
      size: adjustedSize,
      offset: viewportX % adjustedSize // 保持网格对齐
    };
  }

  /**
   * 获取网格图案
   * @private
   * @returns {string} 网格图案类型
   */
  _getGridPattern() {
    switch (this.viewMode) {
      case DateUtils.VIEW_MODES.DAY: return 'dots';
      case DateUtils.VIEW_MODES.WEEK: return 'lines';
      case DateUtils.VIEW_MODES.MONTH: return 'squares';
      case DateUtils.VIEW_MODES.QUARTER: return 'diamonds';
      default: return 'lines';
    }
  }

  /**
   * 获取标签可用宽度
   * @private
   * @param {Object} scale - 时间刻度
   * @returns {number} 可用宽度
   */
  _getAvailableLabelWidth(scale) {
    const unit = DateUtils.getTimeUnit(this.viewMode);
    const scaledPixelsPerDay = this.pixelsPerDay * this.zoomLevel;
    let baseWidth = scaledPixelsPerDay * unit.duration * 0.8; // 留出20%的边距

    // 主要刻度给予更多空间
    if (scale.isMajor) {
      baseWidth *= 1.2;
    }

    return baseWidth;
  }

  /**
   * 更新视图模式（支持动画过渡）
   * @param {string} newViewMode - 新的视图模式
   * @param {Object} options - 切换选项
   * @param {boolean} options.animate - 是否启用动画
   * @param {number} options.duration - 动画持续时间（毫秒）
   * @param {Function} options.onComplete - 完成回调
   * @returns {Promise} 切换完成的Promise
   */
  setViewMode(newViewMode, options = {}) {
    if (this.viewMode === newViewMode) {
      return Promise.resolve();
    }

    const { animate = false, duration = 300, onComplete } = options;
    const oldViewMode = this.viewMode;

    if (!animate) {
      this.viewMode = newViewMode;
      this._isDirty = true;
      this._recalculate();
      if (onComplete) onComplete();
      return Promise.resolve();
    }

    // 动画切换逻辑
    return this._animateViewModeChange(oldViewMode, newViewMode, duration, onComplete);
  }

  /**
   * 动画切换视图模式
   * @private
   * @param {string} fromMode - 原视图模式
   * @param {string} toMode - 目标视图模式
   * @param {number} duration - 动画持续时间
   * @param {Function} onComplete - 完成回调
   * @returns {Promise} 动画完成的Promise
   */
  _animateViewModeChange(fromMode, toMode, duration, onComplete) {
    return new Promise((resolve) => {
      const startTime = performance.now();
      const fromPixelsPerDay = this._getViewModePixelsPerDay(fromMode);
      const toPixelsPerDay = this._getViewModePixelsPerDay(toMode);

      const animate = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeProgress = this._easeInOutCubic(progress);

        // 插值计算当前像素密度
        const currentPixelsPerDay = fromPixelsPerDay + (toPixelsPerDay - fromPixelsPerDay) * easeProgress;

        // 临时更新像素密度
        this.pixelsPerDay = currentPixelsPerDay / this.zoomLevel;
        this._isDirty = true;
        this._recalculate();

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // 动画完成，设置最终状态
          this.viewMode = toMode;
          this.pixelsPerDay = this._getViewModePixelsPerDay(toMode) / this.zoomLevel;
          this._isDirty = true;
          this._recalculate();

          if (onComplete) onComplete();
          resolve();
        }
      };

      requestAnimationFrame(animate);
    });
  }

  /**
   * 获取视图模式对应的像素密度
   * @private
   * @param {string} viewMode - 视图模式
   * @returns {number} 像素密度
   */
  _getViewModePixelsPerDay(viewMode) {
    const baseDensities = {
      [DateUtils.VIEW_MODES.DAY]: 30,
      [DateUtils.VIEW_MODES.WEEK]: 120,
      [DateUtils.VIEW_MODES.MONTH]: 150,
      [DateUtils.VIEW_MODES.QUARTER]: 200
    };

    return baseDensities[viewMode] || 30;
  }

  /**
   * 缓动函数：三次贝塞尔曲线
   * @private
   * @param {number} t - 进度值 (0-1)
   * @returns {number} 缓动后的值
   */
  _easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * 更新缩放级别
   * @param {number} newZoomLevel - 新的缩放级别
   */
  setZoomLevel(newZoomLevel) {
    if (this.zoomLevel === newZoomLevel) return;

    this.zoomLevel = Math.max(0.1, Math.min(10.0, newZoomLevel));
    this._isDirty = true;
    this._recalculate();
  }

  /**
   * 更新日期范围
   * @param {Date} startDate - 新的开始日期
   * @param {Date} endDate - 新的结束日期
   */
  setDateRange(startDate, endDate) {
    this.startDate = new Date(startDate);
    this.endDate = new Date(endDate);
    this._isDirty = true;
    this._recalculate();
  }

  /**
   * 更新视口信息
   * @param {Object} viewport - 视口配置
   */
  setViewport(viewport) {
    this.viewport = { ...this.viewport, ...viewport };
  }

  /**
   * 重新计算（当配置发生变化时）
   * @private
   */
  _recalculate() {
    if (!this._isDirty) return;

    this._calculateScales();
    this._calculateDimensions();
    this._isDirty = false;
  }

  /**
   * 获取时间轴总宽度
   * @returns {number} 总宽度（像素）
   */
  getTotalWidth() {
    if (this._isDirty) this._recalculate();
    return this._totalWidth;
  }

  /**
   * 获取所有时间刻度
   * @returns {Array} 时间刻度数组
   */
  getAllScales() {
    if (this._isDirty) this._recalculate();
    return [...this._scales];
  }

  /**
   * 获取可视区域信息（简化版，向后兼容）
   * @param {number} viewportX - 视口X偏移
   * @param {number} viewportWidth - 视口宽度
   * @returns {Object} 可视区域信息
   */
  getVisibleRange(viewportX = this.viewport.scrollX, viewportWidth = this.viewport.width) {
    const detailed = this.getDetailedVisibleRange(viewportX, viewportWidth);
    return {
      viewport: detailed.horizontal.viewport,
      buffer: detailed.horizontal.buffer,
      dates: detailed.dates
    };
  }

  /**
   * 检查指定区域是否在可视范围内
   * @param {number} startX - 区域开始X坐标
   * @param {number} endX - 区域结束X坐标
   * @param {Object} options - 检查选项
   * @returns {Object} 可见性信息
   */
  isRangeVisible(startX, endX, options = {}) {
    const {
      viewportX = this.viewport.scrollX,
      viewportWidth = this.viewport.width,
      includeBuffer = true,
      threshold = 0
    } = options;

    const bufferX = includeBuffer ? this.bufferConfig.horizontal : 0;
    const visibleStart = viewportX - bufferX - threshold;
    const visibleEnd = viewportX + viewportWidth + bufferX + threshold;

    const isFullyVisible = startX >= visibleStart && endX <= visibleEnd;
    const isPartiallyVisible = !(endX < visibleStart || startX > visibleEnd);
    const visibleWidth = isPartiallyVisible ?
      Math.min(endX, visibleEnd) - Math.max(startX, visibleStart) : 0;

    return {
      isFullyVisible,
      isPartiallyVisible,
      isVisible: isPartiallyVisible,
      visibleWidth,
      visiblePercentage: (endX - startX) > 0 ? (visibleWidth / (endX - startX)) * 100 : 0,
      distanceFromViewport: isPartiallyVisible ? 0 :
        Math.min(Math.abs(startX - visibleEnd), Math.abs(endX - visibleStart))
    };
  }

  /**
   * 获取最优的滚动位置
   * @param {Date} targetDate - 目标日期
   * @param {Object} options - 滚动选项
   * @returns {Object} 滚动位置信息
   */
  getOptimalScrollPosition(targetDate, options = {}) {
    const {
      alignment = 'center', // 'start', 'center', 'end'
      viewportWidth = this.viewport.width,
      margin = 50
    } = options;

    const targetX = this.dateToX(targetDate, { subPixel: true });
    const totalWidth = this.getTotalWidth();

    let scrollX;

    switch (alignment) {
      case 'start':
        scrollX = Math.max(0, targetX - margin);
        break;
      case 'end':
        scrollX = Math.max(0, Math.min(totalWidth - viewportWidth, targetX - viewportWidth + margin));
        break;
      case 'center':
      default:
        scrollX = Math.max(0, Math.min(totalWidth - viewportWidth, targetX - viewportWidth / 2));
        break;
    }

    return {
      scrollX,
      targetX,
      alignment,
      isInBounds: scrollX >= 0 && scrollX <= totalWidth - viewportWidth,
      willBeVisible: this.isRangeVisible(targetX, targetX, {
        viewportX: scrollX,
        viewportWidth,
        includeBuffer: false
      }).isVisible
    };
  }

  /**
   * 计算虚拟滚动的渲染范围
   * @param {number} itemHeight - 单个项目高度
   * @param {number} totalItems - 总项目数
   * @param {Object} viewport - 视口信息
   * @returns {Object} 渲染范围
   */
  calculateVirtualScrollRange(itemHeight, totalItems, viewport = {}) {
    const {
      scrollY = this.viewport.scrollY,
      height = this.viewport.height
    } = viewport;

    const bufferItems = Math.ceil(this.bufferConfig.vertical / itemHeight);
    const visibleItems = Math.ceil(height / itemHeight);

    const startIndex = Math.max(0, Math.floor(scrollY / itemHeight) - bufferItems);
    const endIndex = Math.min(totalItems - 1, startIndex + visibleItems + 2 * bufferItems);

    const renderCount = endIndex - startIndex + 1;
    const totalHeight = totalItems * itemHeight;
    const offsetY = startIndex * itemHeight;

    return {
      startIndex,
      endIndex,
      renderCount,
      totalHeight,
      offsetY,
      visibleStartIndex: Math.max(startIndex, Math.floor(scrollY / itemHeight)),
      visibleEndIndex: Math.min(endIndex, Math.ceil((scrollY + height) / itemHeight)),
      performance: {
        renderRatio: totalItems > 0 ? (renderCount / totalItems) : 0,
        memoryUsage: renderCount * itemHeight,
        isOptimal: renderCount <= visibleItems * 3 // 不超过3倍可见项目数
      }
    };
  }

  /**
   * 查找最接近指定X坐标的刻度
   * @param {number} x - X坐标
   * @returns {Object|null} 最接近的刻度
   */
  findNearestScale(x) {
    if (this._scales.length === 0) return null;

    let nearest = this._scales[0];
    let minDistance = Math.abs(x - nearest.x);

    for (let i = 1; i < this._scales.length; i++) {
      const distance = Math.abs(x - this._scales[i].x);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = this._scales[i];
      }
    }

    return nearest;
  }

  /**
   * 获取性能统计信息
   * @returns {Object} 性能统计
   */
  getPerformanceStats() {
    return {
      totalScales: this._scales.length,
      totalWidth: this._totalWidth,
      viewMode: this.viewMode,
      zoomLevel: this.zoomLevel,
      pixelsPerDay: this.pixelsPerDay * this.zoomLevel,
      dateRange: {
        start: this.startDate,
        end: this.endDate,
        days: DateUtils.getDaysDiff(this.startDate, this.endDate)
      }
    };
  }
}
