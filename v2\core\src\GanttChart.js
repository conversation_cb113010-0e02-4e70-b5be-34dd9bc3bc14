import EventEmitter from "./utils/EventEmitter.js";
import DateUtils from "./utils/DateUtils.js";
import SvgUtils from "./utils/SvgUtils.js";
import TimeScale from "./utils/TimeScale.js";
import TaskItem from "./dataStructures/TaskItem.js";
import DataValidator from "./dataStructures/DataValidator.js";
import Timeline from "./dataStructures/Timeline.js";
import Milestone from "./dataStructures/Milestone.js";
import DataTransformer from "./dataStructures/DataTransformer.js";

class GanttChart extends EventEmitter {
  // 构造函数
  constructor(containerId, options = {}) {
    super();

    // 验证浏览器 SVG 支持
    if (!SvgUtils.isSvgSupported()) {
      throw new Error("Browser does not support SVG");
    }

    if (!containerId) {
      throw new Error("Container element is required");
    }
    this.container = typeof containerId === "string" ? document.getElementById(containerId) : containerId;
    if (!this.container) {
      throw new Error(`Container element "${containerId}" not found`);
    }

    this.options = this.mergeDefaultOptions(options);

    // 数据管理
    this.rawData = options.data || [];
    this.tasks = this.initializeData(this.rawData);
    // this.data = options.data || this.generateSampleData();

    // 时间轴管理
    this.timeScale = null;
    this.dateRange = this.calculateDateRange();

    // DOM 元素引用
    this.elements = {};

    // 状态管理
    this.state = {
      viewMode: this.options.viewMode,
      selectedTasks: new Set(),
      selectedMilestones: new Set(),
      scrollPosition: { x: 0, y: 0 },
      zoomLevel: 1.0,
      isDragging: false,
      dragTarget: null,
      sorting: null, // 排序状态 { column: string, direction: 'asc'|'desc' }
    };

    // 性能监控
    this.performance = {
      renderStart: 0,
      renderEnd: 0,
      lastFrameTime: 0,
    };

    this.init();
  }

  /**
   * 合并默认配置
   */
  mergeDefaultOptions(options) {
    const defaults = {
      // 基础配置
      viewMode: DateUtils.VIEW_MODES.DAY,
      rowHeight: 40,
      pixelsPerDay: 30,

      // 日期范围（自动计算或手动指定）
      startDate: null,
      endDate: null,

      // 表格配置
      taskList: {
        display: true,
        width: 300,
        columns: [{ key: "id", title: "ID", width: 40 }],
      },

      // 时间轴配置
      timeline: {
        showWeekday: true, // 是否在日视图中显示周几
        adaptiveWidth: true, // 是否根据内容自动调整宽度
        minScaleWidth: 30, // 最小刻度宽度
        maxScaleWidth: 80, // 最大刻度宽度
        weekdayFormat: 'short', // 周几显示格式: 'short'(周一) | 'min'(一) | 'abbr'(Mon)
      },

      // 样式配置
      theme: {
        mode: "light", // "light" | "dark" | "auto"
        customClass: "", // 自定义CSS类名
      },
      colors: {
        primary: "#4A90E2",
        success: "#7ED321",
        warning: "#F5A623",
        danger: "#D0021B",
        background: "#ffffff",
        grid: "#f0f0f0",
        text: "#333333",
      },

      // 功能开关
      enableVirtualScroll: false, // Step 2 暂不启用
      enableMilestones: true,
      enableDragDrop: false,
      enableDependencies: false, // 后续步骤实现
      enableMultiTimeline: true,

      // 性能配置
      maxVisibleTasks: 1000,
      renderBatchSize: 50,

      // 事件回调
      onTaskClick: null,
      onTaskSelect: null,
      onMilestoneClick: null,
      onDataChange: null,
      onRenderComplete: null,
    };

    return { ...defaults, ...options };
  }

  /**
   * 初始化数据
   */
  initializeData(rawData) {
    try {
      // 数据验证
      const validation = DataValidator.validateGanttData(rawData);
      if (!validation.valid) {
        console.warn("数据验证失败:", validation.errors);
        this.emit(EventEmitter.EVENTS.DATA_ERROR, validation.errors);
      }

      if (validation.warnings.length > 0) {
        console.warn("数据警告:", validation.warnings);
        this.emit(EventEmitter.EVENTS.WARNING, validation.warnings);
      }

      // 转换数据为 TaskItem 实例
      return rawData.map((taskData) => {
        try {
          return new TaskItem(taskData);
        } catch (error) {
          console.error("任务数据转换失败:", error, taskData);
          // 返回基本任务作为降级处理
          return new TaskItem({
            name: taskData.name || "未命名任务",
            startDate: taskData.startDate || DateUtils.today(),
            endDate: taskData.endDate || DateUtils.addDays(DateUtils.today(), 1),
            ...taskData,
          });
        }
      });
    } catch (error) {
      console.error("数据初始化失败:", error);
      this.emit(EventEmitter.EVENTS.DATA_ERROR, error);
      return [];
    }
  }

  /**
   * 计算日期范围
   */
  calculateDateRange() {
    if (this.options.startDate && this.options.endDate) {
      return {
        start: DateUtils.parseDate(this.options.startDate),
        end: DateUtils.parseDate(this.options.endDate),
      };
    }

    if (this.tasks.length === 0) {
      const today = DateUtils.today();
      return {
        start: today,
        end: DateUtils.addDays(today, 90),
      };
    }

    const dates = this.tasks.reduce((acc, task) => {
      if (task.startDate) acc.push(task.startDate);
      if (task.endDate) acc.push(task.endDate);

      // 包含里程碑日期
      task.getAllMilestones().forEach((milestone) => {
        if (milestone.date) acc.push(milestone.date);
      });

      return acc;
    }, []);

    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));

    // 添加缓冲区
    return {
      start: DateUtils.addDays(minDate, -7),
      end: DateUtils.addDays(maxDate, 14),
    };
  }

  /**
   * 生成示例数据
   */
  generateSampleData() {
    return [
      {
        id: "task-1",
        name: "项目启动",
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        duration: 5,
        progress: 1.0,
        assignee: "张三",
        level: 0,
        status: "completed",
        customFields: {
          priority: "high",
          department: "项目组",
        },
        milestones: [
          {
            id: "milestone-1",
            name: "项目启动会",
            date: "2024-01-02",
            type: "review",
          },
        ],
      },
      {
        id: "task-2",
        name: "需求分析",
        startDate: "2024-01-06",
        endDate: "2024-01-20",
        duration: 15,
        progress: 0.8,
        assignee: "李四",
        level: 1,
        status: "in-progress",
        customFields: {
          priority: "high",
          department: "产品组",
        },
        milestones: [
          {
            id: "milestone-2",
            name: "需求评审",
            date: "2024-01-15",
            type: "review",
          },
        ],
      },
      {
        id: "task-3",
        name: "系统设计",
        startDate: "2024-01-21",
        endDate: "2024-02-10",
        duration: 21,
        progress: 0.6,
        assignee: "王五",
        level: 1,
        status: "in-progress",
        customFields: {
          priority: "medium",
          department: "架构组",
        },
        milestones: [
          {
            id: "milestone-3",
            name: "架构评审",
            date: "2024-02-05",
            type: "review",
          },
        ],
      },
      {
        id: "task-4",
        name: "开发实现",
        startDate: "2024-02-11",
        endDate: "2024-03-15",
        duration: 33,
        progress: 0.3,
        assignee: "赵六",
        level: 0,
        status: "in-progress",
        customFields: {
          priority: "high",
          department: "开发组",
        },
        milestones: [
          {
            id: "milestone-4",
            name: "代码评审",
            date: "2024-03-01",
            type: "review",
          },
          {
            id: "milestone-5",
            name: "功能交付",
            date: "2024-03-10",
            type: "delivery",
          },
        ],
      },
      {
        id: "task-5",
        name: "测试验收",
        startDate: "2024-03-16",
        endDate: "2024-03-30",
        duration: 15,
        progress: 0.0,
        assignee: "孙七",
        level: 0,
        status: "pending",
        customFields: {
          priority: "medium",
          department: "测试组",
        },
        milestones: [
          {
            id: "milestone-6",
            name: "测试完成",
            date: "2024-03-25",
            type: "approval",
          },
        ],
      },
    ];
  }

  /**
   * 初始化时间刻度
   */
  init() {
    this.emit(EventEmitter.EVENTS.INIT);
    console.log("Initializing GanttChart with enhanced features...");

    try {
      this.performance.renderStart = performance.now();

      this.createLayout();
      this.applyTheme();
      this.initializeTimeScale();
      this.renderTable();
      this.renderChart();
      this.bindEvents();
      this.updateStatus();

      this.performance.renderEnd = performance.now();

      console.log(
        `GanttChart initialized successfully! (${Math.round(
          this.performance.renderEnd - this.performance.renderStart
        )}ms)`
      );

      this.emit(EventEmitter.EVENTS.READY);

      if (this.options.onRenderComplete) {
        this.options.onRenderComplete(this.getStats());
      }
    } catch (error) {
      console.error("GanttChart initialization failed:", error);
      this.emit(EventEmitter.EVENTS.ERROR, error);
      throw error;
    }
  }

  /**
   * 创建基础布局
   */
  createLayout() {
    this.container.innerHTML = "";
    this.container.className = "gantt-container";

    const layout = `
      <div class="gantt-header">
        <div class="gantt-toolbar">
          <div class="gantt-view-controls">
            ${Object.values(DateUtils.VIEW_MODES)
              .map(
                (mode) =>
                  `<button class="gantt-btn ${this.state.viewMode === mode ? "active" : ""}" 
                      data-view="${mode}">${DateUtils.getTimeUnit(mode).name}</button>`
              )
              .join("")}
          </div>
          <div class="gantt-actions">
            <button class="gantt-btn" id="gantt-zoom-in">放大</button>
            <button class="gantt-btn" id="gantt-zoom-out">缩小</button>
            <button class="gantt-btn" id="gantt-fit-view">适应窗口</button>
            <button class="gantt-btn" id="gantt-refresh">刷新</button>
            <button class="gantt-btn" id="gantt-export">导出</button>
          </div>
        </div>
      </div>
      <div class="gantt-main">
        <div class="gantt-left-panel" style="width: ${this.options.taskList.width}px">
          <div class="gantt-table-header"></div>
          <div class="gantt-table-body"></div>
        </div>
        
        <div class="gantt-splitter"></div>
          
        <div class="gantt-right-panel">
          <div class="gantt-timeline-header">
            <div class="gantt-timeline-scales"></div>
          </div>
          <div class="gantt-chart-body">
            <svg class="gantt-svg">
              <!-- SVG 内容将通过 SvgUtils 创建 -->
            </svg>
          </div>
        </div>
      </div>
      
      <div class="gantt-footer">
        <div class="gantt-status">
          <span>准备中...</span>
        </div>
      </div>
    `;
    //     <span>任务数: ${this.data.length}</span>
    // <span>视图: ${this.state.viewMode}</span>
    // <span>状态: 就绪</span>

    this.container.innerHTML = layout;

    // 保存关键DOM引用
    this.elements = {
      header: this.container.querySelector(".gantt-header"),
      toolbar: this.container.querySelector(".gantt-toolbar"),
      leftPanel: this.container.querySelector(".gantt-left-panel"),
      rightPanel: this.container.querySelector(".gantt-right-panel"),
      tableHeader: this.container.querySelector(".gantt-table-header"),
      tableBody: this.container.querySelector(".gantt-table-body"),
      timelineHeader: this.container.querySelector(".gantt-timeline-header"),
      timelineScales: this.container.querySelector(".gantt-timeline-scales"),
      chartBody: this.container.querySelector(".gantt-chart-body"),
      svg: this.container.querySelector(".gantt-svg"),
      footer: this.container.querySelector(".gantt-footer"),
      splitter: this.container.querySelector(".gantt-splitter"),
      status: this.container.querySelector(".gantt-status"),
    };

    // 初始化 SVG
    this.initializeSvg();
    console.log("Layout created successfully");
  }

  /**
   * 初始化 SVG 容器
   */
  initializeSvg() {
    const svg = this.elements.svg;

    // 清空现有内容
    SvgUtils.clearContainer(svg);

    // 设置 SVG 属性
    SvgUtils.setAttributes(svg, {
      width: "100%",
      height: "100%",
      class: "gantt-svg",
    });

    // 添加定义区域
    const defs = SvgUtils.createDefs();
    svg.appendChild(defs);

    // 创建分层结构
    const layers = [
      "gantt-background-layer",
      "gantt-grid-layer",
      "gantt-dependencies-layer",
      "gantt-tasks-layer",
      "gantt-milestones-layer",
      "gantt-overlay-layer",
    ];

    layers.forEach((layerClass) => {
      const layer = SvgUtils.createGroup({ class: layerClass });
      svg.appendChild(layer);
    });

    console.log("SVG initialized with layers");
  }

  /**
   * 初始化时间轴（使用新的TimeScale系统）
   */
  initializeTimeScale() {
    // 根据时间轴配置调整像素密度
    let adjustedPixelsPerDay = this.options.pixelsPerDay;

    if (this.options.timeline.showWeekday &&
        this.state.viewMode === DateUtils.VIEW_MODES.DAY &&
        this.options.timeline.adaptiveWidth) {
      // 当显示周几时，增加像素密度以提供足够空间
      adjustedPixelsPerDay = Math.max(adjustedPixelsPerDay, this.options.timeline.minScaleWidth);
    }

    // 创建新的TimeScale实例
    this.timeScale = new TimeScale({
      startDate: this.dateRange.start,
      endDate: this.dateRange.end,
      viewMode: this.state.viewMode,
      pixelsPerDay: adjustedPixelsPerDay,
      zoomLevel: this.state.zoomLevel,
      viewport: {
        width: this.elements.chartBody?.offsetWidth || 1000,
        height: this.elements.chartBody?.offsetHeight || 600,
        scrollX: this.elements.chartBody?.scrollLeft || 0,
        scrollY: this.elements.chartBody?.scrollTop || 0
      },
      gridConfig: {
        showMajorGrid: true,
        showMinorGrid: true,
        adaptiveDensity: true
      },
      timelineConfig: this.options.timeline
    });

    // 为了向后兼容，保留旧的scales数组格式
    this._legacyScales = this.timeScale.getAllScales();

    console.log(`TimeScale initialized: ${this._legacyScales.length} scales, total width: ${this.timeScale.getTotalWidth()}px, pixelsPerDay: ${adjustedPixelsPerDay}`);
  }

  /**
   * 渲染表格
   */
  renderTable() {
    this.renderTableHeader();
    this.renderTableBody();
  }

  /**
   * 渲染表格头部（包含任务列表头部和时间轴头部）
   */
  renderTableHeader() {
    // 渲染左侧任务列表头部
    this.renderTaskListHeader();

    // 渲染右侧时间轴头部
    this.renderTimelineHeader();
  }

  /**
   * 渲染任务列表头部（优化版）
   */
  renderTaskListHeader() {
    if (!this.elements.tableHeader) {
      console.warn('Table header element not found');
      return;
    }

    // 生成优化的头部HTML
    const headerHtml = this.options.taskList.columns
      .map((col, index) => {
        const isFirst = index === 0;
        const isLast = index === this.options.taskList.columns.length - 1;
        const sortable = col.sortable !== false;
        const resizable = col.resizable !== false;

        // 获取列的当前排序状态
        const sortDirection = this._getColumnSortDirection(col.key);
        const sortClass = sortDirection ? `sorted sorted-${sortDirection}` : '';

        // 构建CSS类
        const cellClasses = [
          'gantt-table-header-cell',
          isFirst ? 'first-column' : '',
          isLast ? 'last-column' : '',
          sortable ? 'sortable' : '',
          resizable ? 'resizable' : '',
          sortClass
        ].filter(Boolean).join(' ');

        // 构建样式
        const cellStyle = `width: ${col.width}px; min-width: ${col.minWidth || 80}px; max-width: ${col.maxWidth || 400}px;`;

        // 构建内容
        const content = this._generateHeaderCellContent(col, sortDirection, sortable);

        return `
          <div class="${cellClasses}"
               style="${cellStyle}"
               data-column="${col.key}"
               data-sortable="${sortable}"
               data-resizable="${resizable}"
               title="${col.description || col.title}">
            ${content}
            ${resizable ? '<div class="column-resizer"></div>' : ''}
          </div>
        `;
      })
      .join("");

    this.elements.tableHeader.innerHTML = headerHtml;

    // 绑定头部交互事件
    this._bindHeaderEvents();
  }

  /**
   * 生成头部单元格内容
   * @private
   */
  _generateHeaderCellContent(col, sortDirection, sortable) {
    const icon = col.icon ? `<i class="header-icon ${col.icon}"></i>` : '';
    const title = `<span class="header-title">${col.title}</span>`;
    const sortIcon = sortable ? this._getSortIcon(sortDirection) : '';

    return `
      <div class="header-content">
        ${icon}
        ${title}
        ${sortIcon}
      </div>
    `;
  }

  /**
   * 获取排序图标
   * @private
   */
  _getSortIcon(direction) {
    const icons = {
      asc: '<i class="sort-icon sort-asc">↑</i>',
      desc: '<i class="sort-icon sort-desc">↓</i>',
      none: '<i class="sort-icon sort-none">↕</i>'
    };

    return `<span class="sort-indicator">${icons[direction] || icons.none}</span>`;
  }

  /**
   * 获取列的排序方向
   * @private
   */
  _getColumnSortDirection(columnKey) {
    if (!this.state.sorting || this.state.sorting.column !== columnKey) {
      return null;
    }
    return this.state.sorting.direction;
  }

  /**
   * 绑定头部交互事件
   * @private
   */
  _bindHeaderEvents() {
    const headerCells = this.elements.tableHeader.querySelectorAll('.gantt-table-header-cell');

    headerCells.forEach(cell => {
      // 排序事件
      if (cell.dataset.sortable === 'true') {
        cell.addEventListener('click', (e) => {
          if (!e.target.closest('.column-resizer')) {
            this._handleColumnSort(cell.dataset.column);
          }
        });
      }

      // 列宽调整事件
      const resizer = cell.querySelector('.column-resizer');
      if (resizer) {
        this._bindColumnResizer(resizer, cell);
      }

      // 悬停效果
      cell.addEventListener('mouseenter', () => {
        cell.classList.add('hover');
      });

      cell.addEventListener('mouseleave', () => {
        cell.classList.remove('hover');
      });
    });
  }

  /**
   * 处理列排序
   * @private
   */
  _handleColumnSort(columnKey) {
    const currentSort = this.state.sorting;
    let newDirection = 'asc';

    if (currentSort && currentSort.column === columnKey) {
      newDirection = currentSort.direction === 'asc' ? 'desc' : 'asc';
    }

    this.state.sorting = {
      column: columnKey,
      direction: newDirection
    };

    // 重新渲染头部以更新排序指示器
    this.renderTaskListHeader();

    // 触发排序事件
    this.emit(EventEmitter.EVENTS.COLUMN_SORT, {
      column: columnKey,
      direction: newDirection
    });

    console.log(`Column sorted: ${columnKey} ${newDirection}`);
  }

  /**
   * 绑定列宽调整器
   * @private
   */
  _bindColumnResizer(resizer, cell) {
    let isResizing = false;
    let startX = 0;
    let startWidth = 0;

    const startResize = (e) => {
      isResizing = true;
      startX = e.clientX;
      startWidth = parseInt(window.getComputedStyle(cell).width, 10);

      document.addEventListener('mousemove', doResize);
      document.addEventListener('mouseup', stopResize);

      cell.classList.add('resizing');
      document.body.style.cursor = 'col-resize';
      e.preventDefault();
    };

    const doResize = (e) => {
      if (!isResizing) return;

      const diff = e.clientX - startX;
      const newWidth = Math.max(80, Math.min(400, startWidth + diff));

      cell.style.width = `${newWidth}px`;

      // 更新列配置
      const columnKey = cell.dataset.column;
      const column = this.options.taskList.columns.find(col => col.key === columnKey);
      if (column) {
        column.width = newWidth;
      }
    };

    const stopResize = () => {
      if (!isResizing) return;

      isResizing = false;
      document.removeEventListener('mousemove', doResize);
      document.removeEventListener('mouseup', stopResize);

      cell.classList.remove('resizing');
      document.body.style.cursor = '';

      // 触发列宽改变事件
      this.emit(EventEmitter.EVENTS.COLUMN_RESIZE, {
        column: cell.dataset.column,
        width: parseInt(cell.style.width, 10)
      });

      // 重新渲染以确保布局正确
      this.renderTable();
    };

    resizer.addEventListener('mousedown', startResize);
  }

  /**
   * 渲染时间轴头部（两行日期格子）
   */
  renderTimelineHeader() {
    const timelineHeader = this.elements.timelineHeader;
    const scalesContainer = this.elements.timelineScales;

    if (!timelineHeader || !scalesContainer) {
      console.warn('Timeline header elements not found');
      return;
    }

    // 清空现有内容
    scalesContainer.innerHTML = "";

    if (!this.timeScale) {
      console.warn('TimeScale not initialized, skipping timeline header rendering');
      return;
    }

    // 强制更新TimeScale的视口信息
    this.timeScale.setViewport({
      width: this.elements.chartBody?.offsetWidth || 1000,
      height: this.elements.chartBody?.offsetHeight || 600,
      scrollX: this.elements.chartBody?.scrollLeft || 0,
      scrollY: this.elements.chartBody?.scrollTop || 0
    });

    // 获取当前视口信息
    const viewportX = this.elements.chartBody?.scrollLeft || 0;
    const viewportWidth = this.elements.chartBody?.offsetWidth || 1000;

    // 获取可视区域内的刻度
    const visibleScales = this.timeScale.getVisibleScales(viewportX, viewportWidth, {
      includeBuffer: true,
      maxResults: 200
    });

    // 根据视图模式决定显示结构
    if (this.state.viewMode === DateUtils.VIEW_MODES.YEAR) {
      // 年视图：只显示一行
      const singleRowHtml = this._generateBottomRowScales(visibleScales);
      scalesContainer.innerHTML = `
        <div class="timeline-scales-single-row">
          ${singleRowHtml}
        </div>
      `;
    } else {
      // 其他视图：显示两行结构
      const topRowHtml = this._generateTopRowScales(visibleScales);
      const bottomRowHtml = this._generateBottomRowScales(visibleScales);

      scalesContainer.innerHTML = `
        <div class="timeline-scales-top-row">
          ${topRowHtml}
        </div>
        <div class="timeline-scales-bottom-row">
          ${bottomRowHtml}
        </div>
      `;
    }

    // 设置时间轴头部的总宽度
    const totalWidth = this.timeScale.getTotalWidth();
    scalesContainer.style.width = `${totalWidth}px`;

    console.log(`Timeline header rendered: ${visibleScales.length} visible scales`);
  }

  /**
   * 生成顶部行刻度（月份/年份）
   * @private
   */
  _generateTopRowScales(visibleScales) {
    if (!visibleScales.length) return '';

    const viewMode = this.state.viewMode;
    const topRowScales = [];
    let currentGroup = null;

    visibleScales.forEach((scale) => {
      let groupKey, groupLabel;

      switch (viewMode) {
        case DateUtils.VIEW_MODES.DAY:
        case DateUtils.VIEW_MODES.WEEK:
          // 日视图和周视图：显示月份和年份
          groupKey = `${scale.date.getFullYear()}-${scale.date.getMonth()}`;
          groupLabel = DateUtils.formatDate(scale.date, 'YYYY年MM月');
          break;

        case DateUtils.VIEW_MODES.MONTH:
          // 月视图：显示年份
          groupKey = scale.date.getFullYear().toString();
          groupLabel = `${scale.date.getFullYear()}年`;
          break;

        case DateUtils.VIEW_MODES.QUARTER:
          // 季度视图：显示年份
          groupKey = scale.date.getFullYear().toString();
          groupLabel = `${scale.date.getFullYear()}年`;
          break;

        default:
          groupKey = scale.date.getFullYear().toString();
          groupLabel = `${scale.date.getFullYear()}年`;
      }

      if (!currentGroup || currentGroup.key !== groupKey) {
        // 结束当前组
        if (currentGroup) {
          currentGroup.endX = scale.x;
          topRowScales.push(currentGroup);
        }

        // 开始新组
        currentGroup = {
          key: groupKey,
          label: groupLabel,
          startX: scale.x,
          endX: scale.x,
          scales: [scale]
        };
      } else {
        // 扩展当前组
        currentGroup.endX = scale.x;
        currentGroup.scales.push(scale);
      }
    });

    // 添加最后一个组
    if (currentGroup) {
      // 计算最后一个刻度的结束位置
      const lastScale = currentGroup.scales[currentGroup.scales.length - 1];
      const unit = DateUtils.getTimeUnit(this.state.viewMode);
      const scaledPixelsPerDay = this.options.pixelsPerDay * this.state.zoomLevel;
      currentGroup.endX = lastScale.x + scaledPixelsPerDay * unit.duration;
      topRowScales.push(currentGroup);
    }

    // 生成HTML
    return topRowScales.map(group => {
      const width = group.endX - group.startX;
      return `
        <div class="timeline-scale-group top-row"
             style="left: ${group.startX}px; width: ${width}px"
             data-group-key="${group.key}">
          <div class="scale-group-label">${group.label}</div>
        </div>
      `;
    }).join('');
  }

  /**
   * 生成底部行刻度（具体日期）
   * @private
   */
  _generateBottomRowScales(visibleScales) {
    return visibleScales
      .map((scale, index) => {
        const labelConfig = this.timeScale.getSmartLabelConfig(scale, 100);

        if (!labelConfig.show) return '';

        // 计算刻度宽度 - 使用下一个刻度的位置来确定宽度
        const nextScale = visibleScales[index + 1];
        const scaleWidth = nextScale ? (nextScale.x - scale.x) : (this.options.pixelsPerDay * this.state.zoomLevel);

        const scaleClass = `timeline-scale bottom-row ${scale.isMajor ? 'major' : 'minor'} ${scale.isVisible ? 'visible' : 'buffered'}`;
        const labelStyle = `
          transform: rotate(${labelConfig.rotation}deg);
          font-size: ${labelConfig.fontSize}px;
          font-weight: ${labelConfig.weight};
          color: ${labelConfig.color};
        `;

        // 根据视图模式和配置调整标签内容
        let displayLabel = this._generateScaleLabel(scale, scaleWidth);

        return `
          <div class="${scaleClass}"
               style="left: ${scale.x}px; width: ${scaleWidth}px;"
               data-date="${DateUtils.formatDate(scale.date, 'YYYY-MM-DD')}"
               data-index="${scale.index}">
            <div class="scale-label" style="${labelStyle}">
              ${displayLabel}
            </div>
          </div>
        `;
      })
      .filter(html => html.length > 0)
      .join("");
  }

  /**
   * 生成刻度标签内容
   * @private
   */
  _generateScaleLabel(scale, scaleWidth) {
    const timelineConfig = this.options.timeline;
    let displayLabel = scale.label;

    switch (this.state.viewMode) {
      case DateUtils.VIEW_MODES.DAY:
        displayLabel = DateUtils.formatDate(scale.date, 'DD');

        // 根据配置和宽度决定是否显示周几
        if (timelineConfig.showWeekday && this._shouldShowWeekday(scaleWidth, timelineConfig.weekdayFormat)) {
          const weekday = this._getWeekdayLabel(scale.date, timelineConfig.weekdayFormat);
          displayLabel = `<span class="date-number">${displayLabel}</span><span class="weekday-label">${weekday}</span>`;
        } else {
          displayLabel = `<span class="date-number">${displayLabel}</span>`;
        }
        break;

      case DateUtils.VIEW_MODES.WEEK:
        // 周视图：显示周范围或简化显示
        const weekStart = DateUtils.formatDate(scale.date, 'MM/DD');
        const weekEnd = DateUtils.formatDate(DateUtils.addDays(scale.date, 6), 'MM/DD');

        if (scaleWidth > 80) {
          // 宽度足够：显示完整周范围
          displayLabel = `<span class="date-number">${weekStart}</span><span class="week-range">~${weekEnd}</span>`;
        } else if (scaleWidth > 50) {
          // 中等宽度：显示周开始日期和周标识
          const weekNumber = this._getWeekNumber(scale.date);
          displayLabel = `<span class="date-number">${weekStart}</span><span class="week-label">W${weekNumber}</span>`;
        } else {
          // 窄宽度：只显示周开始日期
          displayLabel = `<span class="date-number">${weekStart}</span>`;
        }
        break;

      case DateUtils.VIEW_MODES.MONTH:
        // 月视图：智能显示月份和年份
        const month = scale.date.getMonth() + 1;
        const year = scale.date.getFullYear();

        if (scaleWidth > 70) {
          // 宽度足够：显示完整月份名称和年份
          const monthName = this._getMonthName(month);
          displayLabel = `<span class="date-number">${monthName}</span><span class="year-label">${year}</span>`;
        } else if (scaleWidth > 45) {
          // 中等宽度：显示数字月份和年份
          displayLabel = `<span class="date-number">${month}月</span><span class="year-label">${year}</span>`;
        } else {
          // 窄宽度：只显示月份
          displayLabel = `<span class="date-number">${month}月</span>`;
        }
        break;

      case DateUtils.VIEW_MODES.QUARTER:
        // 季度视图：显示季度和年份
        const quarter = Math.floor(scale.date.getMonth() / 3) + 1;
        const qYear = scale.date.getFullYear();

        if (scaleWidth > 80) {
          // 宽度足够：显示完整季度名称
          const quarterName = this._getQuarterName(quarter);
          displayLabel = `<span class="date-number">${quarterName}</span><span class="year-label">${qYear}</span>`;
        } else if (scaleWidth > 50) {
          // 中等宽度：显示Q1格式和年份
          displayLabel = `<span class="date-number">Q${quarter}</span><span class="year-label">${qYear}</span>`;
        } else {
          // 窄宽度：只显示Q1格式
          displayLabel = `<span class="date-number">Q${quarter}</span>`;
        }
        break;

      case DateUtils.VIEW_MODES.YEAR:
        // 年视图：显示年份
        const yearValue = scale.date.getFullYear();

        if (scaleWidth > 60) {
          // 宽度足够：显示年份和年标识
          displayLabel = `<span class="date-number">${yearValue}</span><span class="year-suffix">年</span>`;
        } else {
          // 窄宽度：只显示年份数字
          displayLabel = `<span class="date-number">${yearValue}</span>`;
        }
        break;
    }

    return displayLabel;
  }

  /**
   * 判断是否应该显示周几
   * @private
   */
  _shouldShowWeekday(scaleWidth, weekdayFormat) {
    // 只有日视图才显示周几
    if (this.state.viewMode !== DateUtils.VIEW_MODES.DAY) {
      return false;
    }

    // 根据周几格式和刻度宽度判断是否有足够空间
    const minWidthRequired = {
      'short': 45,  // 周一 需要更多宽度
      'min': 35,    // 一 需要较少宽度
      'abbr': 40    // Mon 中等宽度
    };

    return scaleWidth >= (minWidthRequired[weekdayFormat] || minWidthRequired.short);
  }

  /**
   * 获取周几标签
   * @private
   */
  _getWeekdayLabel(date, format) {
    const weekdays = {
      short: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      min: ['日', '一', '二', '三', '四', '五', '六'],
      abbr: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    };

    return weekdays[format] ? weekdays[format][date.getDay()] : weekdays.short[date.getDay()];
  }

  /**
   * 获取周数
   * @private
   */
  _getWeekNumber(date) {
    const startOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - startOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  }

  /**
   * 获取月份名称
   * @private
   */
  _getMonthName(month) {
    const monthNames = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    return monthNames[month - 1] || `${month}月`;
  }

  /**
   * 获取季度名称
   * @private
   */
  _getQuarterName(quarter) {
    const quarterNames = ['第一季度', '第二季度', '第三季度', '第四季度'];
    return quarterNames[quarter - 1] || `Q${quarter}`;
  }



  /**
   * 渲染表格内容
   */
  renderTableBody() {
    const rowsHtml = this.tasks
      .map((task, index) => {
        const cellsHtml = this.options.taskList.columns
          .map((col) => {
            let value = this.getCellValue(task, col);
            let style = `width: ${col.width}px`;

            if (col.key === "name") {
              const indent = task.level * 20;
              style += `; padding-left: ${indent + 12}px`;

              // 添加层级展开/折叠图标
              if (task.isParent()) {
                const icon = task.collapsed ? "▶" : "▼";
                value = `<span class="expand-icon" data-task-id="${task.id}">${icon}</span> ${value}`;
              }
            }

            return `<div class="gantt-table-cell" style="${style}" data-column="${col.key}">${value}</div>`;
          })
          .join("");

        return `
        <div class="gantt-table-row ${this.getTaskRowClasses(task)}" 
             data-task-id="${task.id}" 
             data-task-level="${task.level}"
             style="height: ${task.getRenderHeight()}px">
          ${cellsHtml}
        </div>
      `;
      })
      .join("");

    this.elements.tableBody.innerHTML = rowsHtml;
    console.log("Table body rendered successfully");
  }

  /**
   * 获取单元格值
   */
  getCellValue(task, column) {
    switch (column.key) {
      case "name":
        return task.name;
      case "assignee":
        return task.assignee || "-";
      case "startDate":
        return task.startDate ? DateUtils.formatDate(task.startDate, "MM/DD") : "-";
      case "endDate":
        return task.endDate ? DateUtils.formatDate(task.endDate, "MM/DD") : "-";
      case "duration":
        return `${task.duration}天`;
      case "progress":
        return `${Math.round(task.progress * 100)}%`;
      case "status":
        return this.getStatusLabel(task.status);
      default:
        return task.customFields?.[column.key] || task[column.key] || "-";
    }
  }

  /**
   * 获取状态标签
   */
  getStatusLabel(status) {
    const labels = {
      pending: "待开始",
      "in-progress": "进行中",
      completed: "已完成",
      cancelled: "已取消",
      overdue: "已逾期",
    };
    return labels[status] || status;
  }

  /**
   * 获取任务行CSS类
   */
  getTaskRowClasses(task) {
    const classes = ["gantt-table-row"];

    classes.push(`status-${task.status}`);
    classes.push(`priority-${task.priority}`);

    if (task.isOverdue()) classes.push("overdue");
    if (task.isCritical()) classes.push("critical");
    if (this.state.selectedTasks.has(task.id)) classes.push("selected");
    if (task.collapsed) classes.push("collapsed");

    return classes.join(" ");
  }

  // 渲染图表区域
  renderChart() {
    const svg = this.elements.svg;

    // 计算 SVG 尺寸
    const totalHeight = this.tasks.reduce((height, task) => {
      return height + task.getRenderHeight();
    }, 0);
    svg.style.height = `${Math.max(totalHeight, 400)}px`;

    // 渲染各个层
    this.renderBackground();
    this.renderGrid(); // 渲染网格背景
    this.renderTasks();
    this.renderMilestones();

    console.log("Chart rendered successfully");
  }

  /**
   * 渲染背景
   */
  renderBackground() {
    const backgroundLayer = this.elements.svg.querySelector(".gantt-background-layer");
    SvgUtils.clearContainer(backgroundLayer);

    const totalHeight = this.tasks.reduce((h, task) => h + task.getRenderHeight(), 0);
    const totalWidth = this.timeScale ? this.timeScale.getTotalWidth() : 0;

    if (totalWidth === 0) {
      console.warn('TimeScale not initialized, skipping background rendering');
      return;
    }

    const background = SvgUtils.createRect(0, 0, totalWidth, totalHeight, {
      fill: this.options.colors.background,
      attributes: { class: "chart-background" },
    });

    backgroundLayer.appendChild(background);
  }

  /**
   * 渲染网格（使用新的TimeScale系统）
   */
  renderGrid() {
    const gridLayer = this.elements.svg.querySelector(".gantt-grid-layer");
    SvgUtils.clearContainer(gridLayer);

    const totalHeight = this.tasks.reduce((h, task) => h + task.getRenderHeight(), 0);
    const totalWidth = this.timeScale ? this.timeScale.getTotalWidth() : 0;

    if (!this.timeScale || totalWidth === 0) {
      console.warn('TimeScale not initialized, skipping grid rendering');
      return;
    }

    // 获取当前视口信息
    const viewportX = this.elements.chartBody?.scrollLeft || 0;
    const viewportWidth = this.elements.chartBody?.offsetWidth || 1000;

    // 获取智能网格线配置
    const gridLines = this.timeScale.getGridLines(viewportX, viewportWidth, {
      includeSubGrid: true,
      alignToPixel: true,
      optimizePerformance: true
    });

    // 渲染主要网格线
    gridLines.major.forEach((line) => {
      const svgLine = SvgUtils.createLine(line.x, 0, line.x, totalHeight, {
        stroke: this.options.colors.grid,
        strokeWidth: line.weight || 2,
        opacity: line.opacity || 0.6,
        attributes: {
          class: "grid-line-vertical grid-line-major",
          'data-date': DateUtils.formatDate(line.date, 'YYYY-MM-DD')
        },
      });
      gridLayer.appendChild(svgLine);
    });

    // 渲染次要网格线
    gridLines.minor.forEach((line) => {
      const svgLine = SvgUtils.createLine(line.x, 0, line.x, totalHeight, {
        stroke: this.options.colors.grid,
        strokeWidth: line.weight || 1,
        opacity: line.opacity || 0.3,
        attributes: {
          class: "grid-line-vertical grid-line-minor",
          'data-date': DateUtils.formatDate(line.date, 'YYYY-MM-DD')
        },
      });
      gridLayer.appendChild(svgLine);
    });

    // 渲染子网格线（小时级别）
    if (gridLines.sub && gridLines.sub.length > 0) {
      gridLines.sub.forEach((line) => {
        const svgLine = SvgUtils.createLine(line.x, 0, line.x, totalHeight, {
          stroke: this.options.colors.grid,
          strokeWidth: line.weight || 0.5,
          opacity: line.opacity || 0.15,
          attributes: {
            class: "grid-line-vertical grid-line-sub",
            'data-time': line.label
          },
        });
        gridLayer.appendChild(svgLine);
      });
    }

    // 水平网格线（任务行）
    let currentY = 0;
    this.tasks.forEach((task, index) => {
      if (currentY > 0) {
        const line = SvgUtils.createLine(0, currentY, totalWidth, currentY, {
          stroke: this.options.colors.grid,
          strokeWidth: 1,
          opacity: 0.3,
          attributes: {
            class: "grid-line-horizontal",
            'data-task-index': index
          },
        });
        gridLayer.appendChild(line);
      }
      currentY += task.getRenderHeight();
    });

    // 性能统计
    console.log(`Grid rendered: ${gridLines.major.length} major, ${gridLines.minor.length} minor, ${gridLines.sub?.length || 0} sub lines`);
  }



  /**
   * 渲染任务
   */
  renderTasks() {
    const tasksLayer = this.elements.svg.querySelector(".gantt-tasks-layer");
    SvgUtils.clearContainer(tasksLayer);

    let currentY = 0;

    this.tasks.forEach((task) => {
      const taskGroup = this.renderTask(task, currentY);
      if (taskGroup) {
        tasksLayer.appendChild(taskGroup);
      }
      currentY += task.getRenderHeight();
    });
  }

  /**
   * 渲染单个任务
   */
  renderTask(task, y) {
    if (!task.startDate || !task.endDate) {
      console.warn(`Task ${task.name} missing date information`);
      return null;
    }

    // 计算任务条位置和尺寸
    const startX = this.dateToX(task.startDate);
    const endX = this.dateToX(task.endDate);
    const width = Math.max(endX - startX, 20); // 最小宽度20px
    const height = Math.min(task.rowHeight - 8, 24); // 任务条高度
    const taskY = y + (task.getRenderHeight() - height) / 2;

    // 创建任务组
    const taskGroup = SvgUtils.createGroup({
      class: `task-group ${this.getTaskClasses(task).join(" ")}`,
      "data-task-id": task.id,
    });

    // 创建主任务条
    const taskBar = SvgUtils.createTaskBar(startX, taskY, width, height, {
      id: task.id,
      status: task.status,
      progress: task.progress,
      text: task.name,
      fill: this.getTaskColor(task),
      progressFill: this.getProgressColor(task),
      textColor: "#ffffff",
      fontSize: 11,
    });

    taskGroup.appendChild(taskBar);

    // 如果任务有多个时间线，渲染时间线
    if (task.timelines.length > 1) {
      const timelineRenderInfo = task.getTimelineRenderInfo(y);
      timelineRenderInfo.forEach((info) => {
        const timelineLine = this.renderTimeline(info.timeline, y + info.y, task);
        if (timelineLine) {
          taskGroup.appendChild(timelineLine);
        }
      });
    }

    return taskGroup;
  }

  /**
   * 渲染时间线
   */
  renderTimeline(timeline, y, task) {
    if (!timeline.visible || timeline.collapsed) {
      return null;
    }

    const timelineGroup = SvgUtils.createGroup({
      class: "timeline-group",
      "data-timeline-id": timeline.id,
    });

    // 绘制时间线基线
    const dateRange = timeline.getDateRange();
    if (dateRange.startDate && dateRange.endDate) {
      const startX = this.dateToX(dateRange.startDate);
      const endX = this.dateToX(dateRange.endDate);

      const line = SvgUtils.createLine(startX, y, endX, y, {
        stroke: timeline.color,
        strokeWidth: timeline.lineStyle.strokeWidth || 2,
        dashArray: timeline.lineStyle.strokeDashArray,
        attributes: {
          class: "timeline-base-line",
          opacity: timeline.lineStyle.opacity || 1,
        },
      });

      timelineGroup.appendChild(line);
    }

    return timelineGroup;
  }

  /**
   * 渲染里程碑
   */
  renderMilestones() {
    const milestonesLayer = this.elements.svg.querySelector(".gantt-milestones-layer");
    SvgUtils.clearContainer(milestonesLayer);

    let currentY = 0;

    this.tasks.forEach((task) => {
      const milestones = task.getAllMilestones();

      milestones.forEach((milestone) => {
        if (milestone.date) {
          const milestoneElement = this.renderMilestone(milestone, currentY, task);
          if (milestoneElement) {
            milestonesLayer.appendChild(milestoneElement);
          }
        }
      });

      currentY += task.getRenderHeight();
    });
  }

  /**
   * 渲染单个里程碑
   */
  renderMilestone(milestone, taskY, task) {
    const x = this.dateToX(milestone.date);
    const y = taskY + task.getRenderHeight() / 2;

    const milestoneElement = SvgUtils.createMilestone(x, y, milestone.icon || "diamond", {
      id: milestone.id,
      status: milestone.type,
      showLine: true,
      lineHeight: task.getRenderHeight() - 20,
      attributes: {
        class: `milestone ${milestone.status} ${milestone.priority}`,
        "data-milestone-name": milestone.name,
        "data-milestone-date": DateUtils.formatDate(milestone.date),
      },
    });

    return milestoneElement;
  }

  /**
   * 日期转换为X坐标（使用新的TimeScale系统）
   */
  dateToX(date, options = {}) {
    if (this.timeScale && typeof this.timeScale.dateToX === 'function') {
      return this.timeScale.dateToX(date, options);
    }

    // 降级到旧方法（向后兼容）
    const position = DateUtils.getRelativePosition(date, this.dateRange.start, this.dateRange.end);
    return position * (this._legacyScales?.length || 0) * this.options.pixelsPerDay;
  }

  /**
   * X坐标转换为日期（使用新的TimeScale系统）
   */
  xToDate(x, options = {}) {
    if (this.timeScale && typeof this.timeScale.xToDate === 'function') {
      return this.timeScale.xToDate(x, options);
    }

    // 降级到旧方法（向后兼容）
    const position = x / ((this._legacyScales?.length || 0) * this.options.pixelsPerDay);
    const totalDays = DateUtils.getDaysDiff(this.dateRange.start, this.dateRange.end);
    const daysFromStart = Math.round(totalDays * position);
    return DateUtils.addDays(this.dateRange.start, daysFromStart);
  }

  /**
   * 获取任务样式类
   */
  getTaskClasses(task) {
    const classes = ["task"];

    classes.push(`status-${task.status}`);
    classes.push(`priority-${task.priority}`);

    if (task.isOverdue()) classes.push("overdue");
    if (task.isCritical()) classes.push("critical");
    if (this.state.selectedTasks.has(task.id)) classes.push("selected");

    return classes;
  }

  /**
   * 获取任务颜色
   */
  getTaskColor(task) {
    const colors = {
      completed: this.options.colors.success,
      "in-progress": this.options.colors.primary,
      pending: "#9CA3AF",
      overdue: this.options.colors.danger,
      cancelled: "#6B7280",
    };
    return colors[task.status] || colors.pending;
  }

  /**
   * 获取进度颜色
   */
  getProgressColor(task) {
    if (task.progress >= 1.0) return this.options.colors.success;
    if (task.progress >= 0.5) return this.options.colors.primary;
    return this.options.colors.warning;
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    this.bindScrollEvents();
    this.bindClickEvents();
    this.bindToolbarEvents();
    this.bindResizeEvents();
    console.log("Events bound successfully");
  }

  /**
   * 绑定滚动事件
   */
  bindScrollEvents() {
    // 防抖函数，避免频繁更新时间轴头部
    let timelineHeaderUpdateTimer = null;

    const updateTimelineHeaderDebounced = () => {
      if (timelineHeaderUpdateTimer) {
        clearTimeout(timelineHeaderUpdateTimer);
      }
      timelineHeaderUpdateTimer = setTimeout(() => {
        this.renderTimelineHeader();
      }, 16); // 约60fps
    };

    // 同步滚动
    this.elements.tableBody.addEventListener("scroll", (e) => {
      this.elements.chartBody.scrollTop = e.target.scrollTop;
      this.state.scrollPosition.y = e.target.scrollTop;
      this.emit(EventEmitter.EVENTS.SCROLL, { x: this.state.scrollPosition.x, y: e.target.scrollTop });
    });

    this.elements.chartBody.addEventListener("scroll", (e) => {
      this.elements.tableBody.scrollTop = e.target.scrollTop;
      this.elements.timelineScales.scrollLeft = e.target.scrollLeft;
      this.state.scrollPosition.x = e.target.scrollLeft;
      this.state.scrollPosition.y = e.target.scrollTop;

      // 更新TimeScale的视口信息
      if (this.timeScale && typeof this.timeScale.setViewport === 'function') {
        this.timeScale.setViewport({
          scrollX: e.target.scrollLeft,
          scrollY: e.target.scrollTop,
          width: e.target.offsetWidth,
          height: e.target.offsetHeight
        });
      }

      // 防抖更新时间轴头部
      updateTimelineHeaderDebounced();

      this.emit(EventEmitter.EVENTS.SCROLL, { x: e.target.scrollLeft, y: e.target.scrollTop });
    });
  }

  /**
   * 绑定点击事件
   */
  bindClickEvents() {
    // 任务点击
    this.elements.svg.addEventListener("click", (e) => {
      const taskGroup = e.target.closest("[data-task-id]");
      if (taskGroup) {
        const taskId = taskGroup.dataset.taskId;
        const task = this.getTaskById(taskId);
        if (task) {
          this.selectTask(taskId);
          this.emit(EventEmitter.EVENTS.TASK_CLICK, task);
          if (this.options.onTaskClick) {
            this.options.onTaskClick(task);
          }
        }
        return;
      }

      // 里程碑点击
      const milestoneElement = e.target.closest("[data-milestone-id]");
      if (milestoneElement) {
        const milestoneId = milestoneElement.dataset.milestoneId;
        const milestone = this.getMilestoneById(milestoneId);
        if (milestone) {
          this.selectMilestone(milestoneId);
          this.emit(EventEmitter.EVENTS.MILESTONE_CLICK, milestone);
          if (this.options.onMilestoneClick) {
            this.options.onMilestoneClick(milestone);
          }
        }
      }
    });

    // 表格行点击
    this.elements.tableBody.addEventListener("click", (e) => {
      const row = e.target.closest("[data-task-id]");
      if (row) {
        const taskId = row.dataset.taskId;
        this.selectTask(taskId);
      }

      // 展开/折叠图标点击
      const expandIcon = e.target.closest(".expand-icon");
      if (expandIcon) {
        const taskId = expandIcon.dataset.taskId;
        this.toggleTaskCollapse(taskId);
        e.stopPropagation();
      }
    });
  }

  /**
   * 绑定工具栏事件
   */
  bindToolbarEvents() {
    // 视图模式切换
    this.elements.toolbar.addEventListener("click", (e) => {
      const viewBtn = e.target.closest("[data-view]");
      if (viewBtn) {
        const newViewMode = viewBtn.dataset.view;
        this.changeViewMode(newViewMode);
        return;
      }

      // 其他工具栏按钮
      const buttonId = e.target.id;
      switch (buttonId) {
        case "gantt-zoom-in":
          this.zoomIn();
          break;
        case "gantt-zoom-out":
          this.zoomOut();
          break;
        case "gantt-fit-view":
          this.fitToView();
          break;
        case "gantt-refresh":
          this.refresh();
          break;
        case "gantt-export":
          this.exportChart();
          break;
      }
    });
  }

  /**
   * 绑定窗口大小调整事件
   */
  bindResizeEvents() {
    const resizeHandler = this.debounce(() => {
      this.handleResize();
    }, 250);

    window.addEventListener("resize", resizeHandler);

    // 分割器拖拽
    this.bindSplitterDrag();
  }

  /**
   * 绑定分割器拖拽事件
   */
  bindSplitterDrag() {
    let isDragging = false;
    let startX = 0;
    let startWidth = 0;

    this.elements.splitter.addEventListener("mousedown", (e) => {
      isDragging = true;
      startX = e.clientX;
      startWidth = this.elements.leftPanel.offsetWidth;
      document.body.style.cursor = "col-resize";
      document.body.style.userSelect = "none";
      e.preventDefault();
    });

    document.addEventListener("mousemove", (e) => {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const newWidth = Math.max(200, Math.min(600, startWidth + deltaX));

      this.elements.leftPanel.style.width = `${newWidth}px`;
      this.options.taskList.width = newWidth;
    });

    document.addEventListener("mouseup", () => {
      if (isDragging) {
        isDragging = false;
        document.body.style.cursor = "";
        document.body.style.userSelect = "";
      }
    });
  }

  /**
   * 选择任务
   */
  selectTask(taskId) {
    // 清除之前的选择
    this.state.selectedTasks.clear();
    this.state.selectedTasks.add(taskId);

    // 更新视觉状态
    this.updateSelectionVisual();

    const task = this.getTaskById(taskId);
    this.emit(EventEmitter.EVENTS.TASK_SELECT, task);

    if (this.options.onTaskSelect) {
      this.options.onTaskSelect(task);
    }
  }

  /**
   * 选择里程碑
   */
  selectMilestone(milestoneId) {
    this.state.selectedMilestones.clear();
    this.state.selectedMilestones.add(milestoneId);
    this.updateSelectionVisual();
  }

  /**
   * 切换任务折叠状态
   */
  toggleTaskCollapse(taskId) {
    const task = this.getTaskById(taskId);
    if (!task) return;

    task.toggleCollapsed();
    this.renderTable();
    this.renderChart();

    this.emit(EventEmitter.EVENTS.TASK_UPDATE, task);
  }

  /**
   * 更新选择状态的视觉效果
   */
  updateSelectionVisual() {
    // 移除所有选中状态
    this.container.querySelectorAll(".selected").forEach((el) => {
      el.classList.remove("selected");
    });

    // 添加新的选中状态
    this.state.selectedTasks.forEach((taskId) => {
      const tableRow = this.container.querySelector(`[data-task-id="${taskId}"]`);
      const taskGroup = this.elements.svg.querySelector(`[data-task-id="${taskId}"]`);

      if (tableRow) tableRow.classList.add("selected");
      if (taskGroup) taskGroup.classList.add("selected");
    });

    this.state.selectedMilestones.forEach((milestoneId) => {
      const milestoneElement = this.elements.svg.querySelector(`[data-milestone-id="${milestoneId}"]`);
      if (milestoneElement) milestoneElement.classList.add("selected");
    });
  }

  /**
   * 更新TimeScale配置以适配当前视图模式
   * @private
   */
  _updateTimeScaleConfig() {
    if (!this.timeScale) return;

    // 根据视图模式调整时间轴配置
    const timelineConfig = { ...this.options.timeline };

    // 根据视图模式调整像素密度和显示配置
    let adjustedPixelsPerDay = this.options.pixelsPerDay;

    switch (this.state.viewMode) {
      case DateUtils.VIEW_MODES.DAY:
        // 日视图：支持周几显示，需要适当的像素密度
        if (timelineConfig.showWeekday && timelineConfig.adaptiveWidth) {
          adjustedPixelsPerDay = Math.max(adjustedPixelsPerDay, timelineConfig.minScaleWidth);
        }
        break;

      case DateUtils.VIEW_MODES.WEEK:
        // 周视图：不显示周几，优化像素密度以显示周范围
        timelineConfig.showWeekday = false;
        adjustedPixelsPerDay = Math.max(50, adjustedPixelsPerDay * 1.2); // 增加宽度以显示周范围
        break;

      case DateUtils.VIEW_MODES.MONTH:
        // 月视图：不显示周几，优化像素密度以显示月份和年份
        timelineConfig.showWeekday = false;
        adjustedPixelsPerDay = Math.max(45, adjustedPixelsPerDay * 1.0); // 保持适中宽度
        break;

      case DateUtils.VIEW_MODES.QUARTER:
        // 季度视图：不显示周几，优化像素密度以显示季度和年份
        timelineConfig.showWeekday = false;
        adjustedPixelsPerDay = Math.max(60, adjustedPixelsPerDay * 1.1); // 增加宽度以显示完整季度名称
        break;

      case DateUtils.VIEW_MODES.YEAR:
        // 年视图：不显示周几，使用较大像素密度以显示年份
        timelineConfig.showWeekday = false;
        adjustedPixelsPerDay = Math.max(80, adjustedPixelsPerDay * 1.5); // 较大宽度以显示年份和后缀
        break;
    }

    // 更新TimeScale的配置
    if (this.timeScale.timelineConfig) {
      Object.assign(this.timeScale.timelineConfig, timelineConfig);
    }

    // 如果像素密度发生变化，更新TimeScale
    if (Math.abs(this.timeScale.pixelsPerDay - adjustedPixelsPerDay) > 0.1) {
      this.timeScale.pixelsPerDay = adjustedPixelsPerDay;
      this.timeScale._isDirty = true;
    }

    // 强制重新计算刻度以确保配置生效
    if (this.timeScale._isDirty) {
      this.timeScale._recalculate();
      // 更新legacy scales以保持兼容性
      this._legacyScales = this.timeScale.getAllScales();
    }
  }

  /**
   * 改变视图模式（支持动画）
   */
  changeViewMode(newViewMode, options = {}) {
    if (this.state.viewMode === newViewMode) return Promise.resolve();

    const { animate = false, duration = 300 } = options;
    const oldViewMode = this.state.viewMode;

    // 更新工具栏按钮状态
    this.elements.toolbar.querySelectorAll("[data-view]").forEach((btn) => {
      btn.classList.toggle("active", btn.dataset.view === newViewMode);
    });

    if (this.timeScale && typeof this.timeScale.setViewMode === 'function') {
      // 使用新的TimeScale系统（支持动画）
      return this.timeScale.setViewMode(newViewMode, {
        animate,
        duration,
        onComplete: () => {
          this.state.viewMode = newViewMode;
          // 重新初始化TimeScale以适配新视图模式的显示需求
          this._updateTimeScaleConfig();

          // 使用requestAnimationFrame确保DOM更新完成后再渲染
          requestAnimationFrame(() => {
            // 强制重新渲染时间轴头部
            this.renderTimelineHeader();
            this.renderChart();

            this.emit(EventEmitter.EVENTS.VIEW_CHANGE, {
              from: oldViewMode,
              to: newViewMode,
            });

            console.log(`View mode changed from ${oldViewMode} to ${newViewMode}`);
          });
        }
      });
    } else {
      // 降级到旧方法
      this.state.viewMode = newViewMode;
      this.initializeTimeScale();
      this.renderChart();

      this.emit(EventEmitter.EVENTS.VIEW_CHANGE, {
        from: oldViewMode,
        to: newViewMode,
      });

      console.log(`View mode changed from ${oldViewMode} to ${newViewMode}`);
      return Promise.resolve();
    }
  }

  /**
   * 放大
   */
  zoomIn() {
    this.state.zoomLevel = Math.min(this.state.zoomLevel * 1.2, 3.0);
    this.applyZoom();
  }

  /**
   * 缩小
   */
  zoomOut() {
    this.state.zoomLevel = Math.max(this.state.zoomLevel / 1.2, 0.3);
    this.applyZoom();
  }

  /**
   * 适应窗口
   */
  fitToView() {
    this.state.zoomLevel = 1.0;
    this.applyZoom();

    // 滚动到项目开始位置
    const startX = this.dateToX(this.dateRange.start);
    this.elements.chartBody.scrollLeft = Math.max(0, startX - 100);
  }

  /**
   * 设置缩放级别
   * @param {number} zoomLevel - 缩放级别 (0.3 - 3.0)
   */
  setZoomLevel(zoomLevel) {
    this.state.zoomLevel = Math.max(0.3, Math.min(3.0, zoomLevel));
    this.applyZoom();
  }

  /**
   * 强制刷新时间轴
   * 用于解决缩放或视图切换后时间轴不更新的问题
   */
  forceRefreshTimeline() {
    if (!this.timeScale) return;

    // 强制标记为需要重新计算
    this.timeScale._isDirty = true;

    // 更新配置
    this._updateTimeScaleConfig();

    // 立即重新渲染时间轴
    requestAnimationFrame(() => {
      this.renderTimelineHeader();
      console.log('Timeline force refreshed');
    });
  }

  /**
   * 应用主题
   */
  applyTheme() {
    const themeConfig = this.options.theme;
    const container = this.elements.container;

    if (!container) return;

    // 移除所有主题类
    container.classList.remove('dark-theme', 'light-theme');

    // 移除自定义类
    if (this._currentCustomClass) {
      container.classList.remove(this._currentCustomClass);
    }

    // 应用主题模式
    switch (themeConfig.mode) {
      case 'dark':
        container.classList.add('dark-theme');
        break;
      case 'light':
        container.classList.add('light-theme');
        break;
      case 'auto':
        // 根据系统偏好自动选择
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
          container.classList.add('dark-theme');
        } else {
          container.classList.add('light-theme');
        }
        break;
      default:
        // 默认浅色主题
        container.classList.add('light-theme');
    }

    // 应用自定义类
    if (themeConfig.customClass) {
      container.classList.add(themeConfig.customClass);
      this._currentCustomClass = themeConfig.customClass;
    }

    console.log(`Theme applied: ${themeConfig.mode}${themeConfig.customClass ? ` + ${themeConfig.customClass}` : ''}`);
  }

  /**
   * 设置主题模式
   * @param {string} mode - 主题模式: "light" | "dark" | "auto"
   */
  setTheme(mode) {
    this.options.theme.mode = mode;
    this.applyTheme();
  }

  /**
   * 切换主题模式
   */
  toggleTheme() {
    const currentMode = this.options.theme.mode;
    const newMode = currentMode === 'dark' ? 'light' : 'dark';
    this.setTheme(newMode);
    return newMode;
  }

  /**
   * 应用缩放（使用新的TimeScale系统）
   */
  applyZoom() {
    if (this.timeScale && typeof this.timeScale.setZoomLevel === 'function') {
      // 使用新的TimeScale系统
      this.timeScale.setZoomLevel(this.state.zoomLevel);

      // 更新时间轴配置以适配新的缩放级别
      this._updateTimeScaleConfig();

      // 更新视口信息
      this.timeScale.setViewport({
        width: this.elements.chartBody?.offsetWidth || 1000,
        height: this.elements.chartBody?.offsetHeight || 600,
        scrollX: this.elements.chartBody?.scrollLeft || 0,
        scrollY: this.elements.chartBody?.scrollTop || 0
      });
    } else {
      // 降级到旧方法
      const scaledPixelsPerDay = this.options.pixelsPerDay * this.state.zoomLevel;
      const unit = DateUtils.getTimeUnit(this.state.viewMode);

      if (this._legacyScales) {
        this._legacyScales.forEach((scale, index) => {
          scale.x = index * scaledPixelsPerDay * unit.duration;
        });
      }
    }

    // 使用requestAnimationFrame确保DOM更新完成后再渲染
    requestAnimationFrame(() => {
      // 强制重新渲染时间轴头部
      this.renderTimelineHeader();

      // 重新渲染整个图表
      this.renderChart();

      this.emit(EventEmitter.EVENTS.ZOOM_CHANGE, this.state.zoomLevel);

      console.log(`Zoom applied: ${Math.round(this.state.zoomLevel * 100)}%`);
    });
  }

  /**
   * 刷新甘特图
   */
  refresh() {
    console.log("Refreshing gantt chart...");
    
    try {
      this.performance.renderStart = performance.now();
      
      // 重新初始化数据
      this.dateRange = this.calculateDateRange();
      this.initializeTimeScale();
      
      // 重新渲染
      this.renderTable();
      this.renderChart();
      this.updateStatus();
      
      this.performance.renderEnd = performance.now();
      
      this.emit(EventEmitter.EVENTS.RENDER_END, {
        renderTime: this.performance.renderEnd - this.performance.renderStart
      });
      
      this.showMessage(`甘特图已刷新 (${Math.round(this.performance.renderEnd - this.performance.renderStart)}ms)`);
    } catch (error) {
      console.error("Refresh failed:", error);
      this.emit(EventEmitter.EVENTS.ERROR, error);
      this.showMessage("刷新失败", "error");
    }
  }

  /**
   * 导出图表
   */
  exportChart() {
    try {
      const svgData = new XMLSerializer().serializeToString(this.elements.svg);
      const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
      const url = URL.createObjectURL(svgBlob);
      
      const link = document.createElement("a");
      link.href = url;
      link.download = `gantt-chart-${DateUtils.formatDate(new Date(), "YYYY-MM-DD")}.svg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      this.showMessage("图表已导出");
    } catch (error) {
      console.error("Export failed:", error);
      this.showMessage("导出失败", "error");
    }
  }

  /**
   * 处理窗口大小调整
   */
  handleResize() {
    console.log("Window resized - adjusting layout");
    
    // 延迟重新渲染，避免频繁操作
    setTimeout(() => {
      this.renderChart();
      this.emit(EventEmitter.EVENTS.RESIZE);
    }, 100);
  }

  /**
   * 更新状态栏
   */
  updateStatus() {
    const stats = this.getStats();
    
    this.elements.status.innerHTML = `
      <span>总任务: ${stats.totalTasks}</span>
      <span>已完成: ${stats.completedTasks}</span>
      <span>进行中: ${stats.inProgressTasks}</span>
      <span>待开始: ${stats.pendingTasks}</span>
      <span>里程碑: ${stats.totalMilestones}</span>
      <span>视图: ${DateUtils.getTimeUnit(this.state.viewMode).name}</span>
      <span>缩放: ${Math.round(this.state.zoomLevel * 100)}%</span>
    `;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const stats = {
      totalTasks: this.tasks.length,
      completedTasks: 0,
      inProgressTasks: 0,
      pendingTasks: 0,
      overdueTasks: 0,
      totalMilestones: 0,
      completedMilestones: 0,
      renderTime: this.performance.renderEnd - this.performance.renderStart
    };

    this.tasks.forEach(task => {
      switch (task.status) {
        case "completed":
          stats.completedTasks++;
          break;
        case "in-progress":
          stats.inProgressTasks++;
          break;
        case "pending":
          stats.pendingTasks++;
          break;
      }
      
      if (task.isOverdue()) {
        stats.overdueTasks++;
      }

      const milestones = task.getAllMilestones();
      stats.totalMilestones += milestones.length;
      stats.completedMilestones += milestones.filter(m => m.status === "completed").length;
    });

    return stats;
  }

  /**
   * 显示消息
   */
  showMessage(message, type = "info") {
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // 这里可以扩展为显示Toast消息
    const statusElement = this.elements.status.querySelector("span:last-child");
    if (statusElement) {
      const originalText = statusElement.textContent;
      statusElement.textContent = message;
      statusElement.className = `message message-${type}`;
      
      setTimeout(() => {
        statusElement.textContent = originalText;
        statusElement.className = "";
      }, 3000);
    }
  }

  /**
   * 通过ID获取任务
   */
  getTaskById(taskId) {
    return this.tasks.find(task => task.id === taskId);
  }

  /**
   * 通过ID获取里程碑
   */
  getMilestoneById(milestoneId) {
    for (const task of this.tasks) {
      const milestones = task.getAllMilestones();
      const milestone = milestones.find(m => m.id === milestoneId);
      if (milestone) return milestone;
    }
    return null;
  }

  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
  
  // ========== 公共 API ==========

  /**
   * 设置数据
   */
  setData(data) {
    try {
      this.rawData = data;
      this.tasks = this.initializeData(data);
      this.dateRange = this.calculateDateRange();
      this.initializeTimeScale();
      
      this.renderTable();
      this.renderChart();
      this.updateStatus();
      
      this.emit(EventEmitter.EVENTS.DATA_CHANGE, this.tasks);
      
      if (this.options.onDataChange) {
        this.options.onDataChange(this.tasks);
      }
      
      console.log("Data updated successfully");
    } catch (error) {
      console.error("Set data failed:", error);
      this.emit(EventEmitter.EVENTS.DATA_ERROR, error);
    }
  }

  /**
   * 获取数据
   */
  getData() {
    return this.tasks.map(task => task.toJSON());
  }

  /**
   * 获取选中的任务
   */
  getSelectedTasks() {
    return Array.from(this.state.selectedTasks).map(id => this.getTaskById(id)).filter(Boolean);
  }

  /**
   * 获取选中的里程碑
   */
  getSelectedMilestones() {
    return Array.from(this.state.selectedMilestones).map(id => this.getMilestoneById(id)).filter(Boolean);
  }

  /**
   * 添加任务
   */
  addTask(taskData) {
    try {
      const task = new TaskItem(taskData);
      this.tasks.push(task);
      
      this.dateRange = this.calculateDateRange();
      this.initializeTimeScale();
      
      this.renderTable();
      this.renderChart();
      this.updateStatus();
      
      this.emit(EventEmitter.EVENTS.TASK_ADD, task);
      
      return task;
    } catch (error) {
      console.error("Add task failed:", error);
      this.emit(EventEmitter.EVENTS.ERROR, error);
      return null;
    }
  }

  /**
   * 删除任务
   */
  removeTask(taskId) {
    const index = this.tasks.findIndex(task => task.id === taskId);
    if (index === -1) return false;
    
    const removedTask = this.tasks.splice(index, 1)[0];
    
    // 清除选择状态
    this.state.selectedTasks.delete(taskId);
    
    this.renderTable();
    this.renderChart();
    this.updateStatus();
    
    this.emit(EventEmitter.EVENTS.TASK_DELETE, removedTask);
    
    return true;
  }

  /**
   * 更新任务
   */
  updateTask(taskId, updates) {
    const task = this.getTaskById(taskId);
    if (!task) return false;
    
    try {
      Object.assign(task, updates);
      
      // 重新计算相关属性
      task.calculateDuration();
      task.updateTimelineLayout();
      
      this.renderTable();
      this.renderChart();
      this.updateStatus();
      
      this.emit(EventEmitter.EVENTS.TASK_UPDATE, task);
      
      return true;
    } catch (error) {
      console.error("Update task failed:", error);
      this.emit(EventEmitter.EVENTS.ERROR, error);
      return false;
    }
  }

  /**
   * 滚动到任务
   */
  scrollToTask(taskId) {
    const taskIndex = this.tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) return;
    
    let yPosition = 0;
    for (let i = 0; i < taskIndex; i++) {
      yPosition += this.tasks[i].getRenderHeight();
    }
    
    this.elements.chartBody.scrollTop = yPosition;
    this.elements.tableBody.scrollTop = yPosition;
    
    // 高亮显示任务
    this.selectTask(taskId);
  }

  /**
   * 滚动到日期
   */
  scrollToDate(date) {
    const x = this.dateToX(DateUtils.parseDate(date));
    const containerWidth = this.elements.chartBody.offsetWidth;
    this.elements.chartBody.scrollLeft = Math.max(0, x - containerWidth / 2);
  }

  /**
   * 获取可见的任务
   */
  getVisibleTasks() {
    const scrollTop = this.elements.chartBody.scrollTop;
    const containerHeight = this.elements.chartBody.offsetHeight;
    
    const visibleTasks = [];
    let currentY = 0;
    
    for (const task of this.tasks) {
      const taskHeight = task.getRenderHeight();
      
      if (currentY + taskHeight > scrollTop && currentY < scrollTop + containerHeight) {
        visibleTasks.push(task);
      }
      
      currentY += taskHeight;
      
      if (currentY > scrollTop + containerHeight) {
        break;
      }
    }
    
    return visibleTasks;
  }

  /**
   * 导出数据
   */
  exportData(format = "json") {
    switch (format.toLowerCase()) {
      case "json":
        return JSON.stringify(this.getData(), null, 2);
      case "csv":
        return this.exportToCsv();
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * 导出为CSV格式
   */
  exportToCsv() {
    const headers = ["ID", "名称", "开始日期", "结束日期", "工期", "进度", "状态", "负责人"];
    const rows = [headers.join(",")];
    
    this.tasks.forEach(task => {
      const row = [
        task.id,
        `"${task.name}"`,
        DateUtils.formatDate(task.startDate),
        DateUtils.formatDate(task.endDate),
        task.duration,
        Math.round(task.progress * 100) + "%",
        task.status,
        `"${task.assignee || ""}"`
      ];
      rows.push(row.join(","));
    });
    
    return rows.join("\n");
  }

  /**
   * 销毁甘特图实例
   */
  destroy() {
    try {
      // 移除事件监听器
      window.removeEventListener("resize", this.handleResize);
      
      // 清理定时器
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }
      
      // 清空容器
      this.container.innerHTML = "";
      
      // 清理数据引用
      this.tasks = [];
      this.rawData = [];
      this.timeScale = [];
      this.elements = {};
      
      // 移除所有事件监听器
      this.removeAllListeners();
      
      this.emit(EventEmitter.EVENTS.DESTROY);
      
      console.log("GanttChart destroyed successfully");
    } catch (error) {
      console.error("Destroy failed:", error);
    }
  }
}

window.GanttChart = GanttChart;
export default GanttChart;
